## Pull Request Description

**Summary:** [Provide a brief summary of the changes introduced by this pull request.]

**Related Issue:** [Specify the issue number or describe the problem that this pull request addresses.]

## Proposed Changes

**Description:** [Provide a detailed description of the changes made in this pull request.]

**Motivation and Context:** [Explain the motivation behind these changes and the impact they have on the project.]

## Checklist

Please review and check the following items before submitting your pull request:

- [ ] I have executed the command `flutter pub run build_runner build` to generate the necessary auto routes files.
- [ ] I have verified that the auto route generation did not introduce any errors or warnings.
- [ ] I have tested the changes locally and they are functioning as expected.
- [ ] My code follows the project's coding style and guidelines.
- [ ] All existing and new tests are passing.
- [ ] I have added necessary documentation or updated existing documentation (if applicable).
- [ ] I have added appropriate unit tests or updated existing tests (if applicable).
- [ ] My commits are descriptive and follow the project's commit message conventions.
- [ ] I have rebased my branch on the latest `main` branch.

## Screenshots (if applicable)

**Before:**
[Add screenshots or GIFs showing the state before the changes (if applicable).]

**After:**
[Add screenshots or GIFs showing the state after the changes (if applicable).]

