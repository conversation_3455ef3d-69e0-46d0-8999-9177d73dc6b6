name: Build Flutter APK and Upload to Release

on:
  push:
    tags:
      - '*' # v*.*.* Trigger on tags that follow semantic versioning
    # push:
    #   tags:
    #     - 'v*.*.*' # Trigger on tags that follow semantic versioning

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # Checkout the repository

#      - name: Create env file
#        env:
#          API_BASE_URL: "https://zod-backend-main-hyrap1.laravel.cloud/api/" #${{ secrets.API_BASE_URL }}
#          CONNECT_TIMEOUT: 100000
#          SEND_TIMEOUT: 1000000
#          RECEIVE_TIMEOUT: 1000000
#          RECEIVE_DATA_WHEN_STATUS_ERROR: true
#        run: |
#          echo "{" >> .env.json
#          echo "API_BASE_URL:$API_BASE_URL," >> .env.json
#          echo "CONNECT_TIMEOUT:$CONNECT_TIMEOUT," >> .env.json
#          echo "SEND_TIMEOUT:$SEND_TIMEOUT," >> .env.json
#          echo "RECEIVE_TIMEOUT:$RECEIVE_TIMEOUT," >> .env.json
#          echo "RECEIVE_DATA_WHEN_STATUS_ERROR:$RECEIVE_DATA_WHEN_STATUS_ERROR" >> .env.json
#          echo "}" >> .env.json
#
#      - name: Create Staging env file
#        env:
#          API_BASE_URL: ${{ secrets.STAGING_API_BASE_URL}}
#          CONNECT_TIMEOUT: 100000
#          SEND_TIMEOUT: 1000000
#          RECEIVE_TIMEOUT: 1000000
#          RECEIVE_DATA_WHEN_STATUS_ERROR: true
#        run: |
#          echo "{" >> .env.staging.json
#          echo "API_BASE_URL:$API_BASE_URL," >> .env.staging.json
#          echo "CONNECT_TIMEOUT:$CONNECT_TIMEOUT," >> .env.staging.json
#          echo "SEND_TIMEOUT:$SEND_TIMEOUT," >> .env.staging.json
#          echo "RECEIVE_TIMEOUT:$RECEIVE_TIMEOUT," >> .env.staging.json
#          echo "RECEIVE_DATA_WHEN_STATUS_ERROR:$RECEIVE_DATA_WHEN_STATUS_ERROR" >> .env.staging.json
#          echo "}" >> .env.staging.json

      - name: Checkout code
        uses: actions/checkout@v3

      # Set up Flutter environment
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.27.3' #3.19.0
      - name: Create env.json file
        run: |
          cat <<EOF > env.json
          {
            "API_BASE_URL": "https://zod-backend-main-hyrap1.laravel.cloud/api/",
            "CONNECT_TIMEOUT": "100000",
            "SEND_TIMEOUT": "1000000",
            "RECEIVE_TIMEOUT": "1000000",
            "RECEIVE_DATA_WHEN_STATUS_ERROR": "true"
          }
          EOF

      - name: Verify env.json
        run: cat env.json  # Optional, just to confirm the file creation
      - name: Debug - List Files
        run: ls -lah
      # Install dependencies
      - name: Install dependencies
        run: flutter pub get

      # Build APK
      - name: Build APK
        run: flutter build apk --release  --flavor production --dart-define-from-file=env.json

      # Archive the APK
      - name: Archive APK
        uses: actions/upload-artifact@v4
        with:
          name: app-release
          path: build/app/outputs/flutter-apk/app-production-release.apk

  release:
    needs: build
    runs-on: ubuntu-latest
    steps:
      # Checkout code
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Fetch Commit Messages
        id: commits
        run: |
          COMMITS=$(git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 2>/dev/null || echo "")..HEAD)
          echo "commits=$COMMITS" >> $GITHUB_ENV

      # Download the APK from the build job
      - name: Download APK artifact
        uses: actions/download-artifact@v4
        with:
          name: app-release
          path: build/

      # Upload APK to release page
      - name: Upload APK to GitHub Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: build/app-production-release.apk
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: "v${{ github.run_number }}"
          name: "Release ${{ github.run_number }}"
          # tag: ${{ github.ref_name }}
          # name: "Release ${{ github.ref_name }}"
          body: |
            ## Release Notes
            - APK built successfully for release.
            - Branch: `${{ github.ref_name }}`.
            - Recent commits:
              ${{ env.commits }}
#        body: |
#          This release includes the following:
#          - APK built for release.