# ChargeWalletProvider

The `ChargeWalletProvider` class is responsible for managing the wallet charging state and operations.

## Location

`lib/features/wallet/presentation/providers/charge_wallet_provider.dart`

## Dependencies

- `flutter_riverpod`: For state management
- `WalletChargeDto`: Data model for payment requests
- `PaymentMethodSelectionView`: UI for payment method selection

## Class Structure

```dart
final chargeWalletProvider = StateNotifierProvider.family<ChargeWalletProvider, ChargeWalletState, ChargeWalletAuction>(
  (ref, chargeWalletAuction) => ChargeWalletProvider(
    walletApi: sl<WalletApi>(),
    chargeWalletAuction: chargeWalletAuction,
  ),
);

class ChargeWalletProvider extends StateNotifier<ChargeWalletState> {
  final WalletApi walletApi;
  final ChargeWalletAuction chargeWalletAuction;

  ChargeWalletProvider({
    required this.walletApi,
    required this.chargeWalletAuction,
  }) : super(ChargeWalletState(
          points: 0,
          fee: chargeWalletAuction.fee,
          increment: chargeWalletAuction.increment,
        )) {
    init();
  }

  void init();
  void increasePoints();
  void decreasePoints();
  void chargeWallet(ref);
  void _directChargeWallet(ref);
}
```

## Key Methods

### init

Initializes the provider by fetching points per price data:

```dart
void init() async {
  try {
    final pointsPerPrice = await walletApi.pointsPerPrice();
    
    if (pointsPerPrice.data != null) {
      state = state.copyWith(
        pointsPerPrice: pointsPerPrice.data,
        minChargePoint: pointsPerPrice.data?.minChargePoint ?? 0,
        points: pointsPerPrice.data?.suggestionPoints?.first ?? 0,
      );
    }
  } catch (e) {
    state = state.copyWith(error: e.toString());
  }
}
```

### increasePoints

Increases the points amount:

```dart
void increasePoints() {
  final pointsPerPrice = state.pointsPerPrice;
  if (pointsPerPrice == null) return;
  
  final suggestionPoints = pointsPerPrice.suggestionPoints;
  if (suggestionPoints == null || suggestionPoints.isEmpty) return;
  
  final currentIndex = suggestionPoints.indexOf(state.points);
  if (currentIndex < suggestionPoints.length - 1) {
    state = state.copyWith(points: suggestionPoints[currentIndex + 1]);
  }
}
```

### decreasePoints

Decreases the points amount:

```dart
void decreasePoints() {
  final pointsPerPrice = state.pointsPerPrice;
  if (pointsPerPrice == null) return;
  
  final suggestionPoints = pointsPerPrice.suggestionPoints;
  if (suggestionPoints == null || suggestionPoints.isEmpty) return;
  
  final currentIndex = suggestionPoints.indexOf(state.points);
  if (currentIndex > 0) {
    state = state.copyWith(points: suggestionPoints[currentIndex - 1]);
  }
}
```

### chargeWallet

Shows the payment method selection bottom sheet:

```dart
void chargeWallet(ref) async {
  // Create wallet charge DTO
  WalletChargeDto walletChargeDto =
      WalletChargeDto(state.price, state.points);

  // Show payment method selection as bottom sheet
  showModalBottomSheet(
    context: appKey.currentContext!,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) =>
        PaymentMethodSelectionView(chargeDto: walletChargeDto),
  );
}
```

### _directChargeWallet

Legacy method for direct wallet charging (without payment gateway):

```dart
void _directChargeWallet(ref) async {
  WalletChargeDto walletChargeDto =
      WalletChargeDto(state.price, state.points);
  state = state.copyWith(isLoading: true);
  try {
    final chargeUser = await walletApi.walletCharge(walletChargeDto);

    if (chargeUser.meta.success ?? false) {
      goRouter.pop();
      if (chargeWalletAuction.auction?.isNotJoined ?? false) {
        goRouter.push(JoinToAuctionView.route,
            extra: chargeWalletAuction.auction);
      }

      NotificationAlert.showLocalNotification(
        chargeUser.meta.message,
        type: ToastType.success,
        align: Alignment.topCenter,
      );

      ref.invalidate(meProvider);
    } else {
      NotificationAlert.showLocalNotification(
        chargeUser.meta.message,
        type: ToastType.error,
      );
    }
    state = state.copyWith(isLoading: false);
  } catch (e) {
    state = state.copyWith(isLoading: false, error: e.toString());
    NotificationAlert.showLocalNotification(
      e.toString(),
      type: ToastType.error,
    );
  }
}
```

## Usage Example

```dart
// In ChargeWalletView
Widget build(BuildContext context, WidgetRef ref) {
  final walletState = ref.watch(chargeWalletProvider(chargeWalletAuction));
  
  return Scaffold(
    // ...
    body: Column(
      children: [
        // Points selection
        Row(
          children: [
            IconButton(
              icon: SvgPicture.asset(AppIcons.walletMinus),
              onPressed: ref
                  .read(chargeWalletProvider(chargeWalletAuction).notifier)
                  .decreasePoints,
            ),
            Text("${walletState.points}"),
            IconButton(
              icon: SvgPicture.asset(AppIcons.walletPlus),
              onPressed: ref
                  .read(chargeWalletProvider(chargeWalletAuction).notifier)
                  .increasePoints,
            ),
          ],
        ),
        
        // Buy button
        ElevatedButton(
          onPressed: () => ref
              .read(chargeWalletProvider(chargeWalletAuction).notifier)
              .chargeWallet(ref),
          child: Text("Buy Points"),
        ),
      ],
    ),
  );
}
```
