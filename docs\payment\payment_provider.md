# PaymentProvider

The `PaymentProvider` class is responsible for managing the payment state and handling payment operations.

## Location

`lib/features/payment/presentation/providers/payment_provider.dart`

## Dependencies

- `flutter_riverpod`: For state management
- `PaymentService`: For payment processing
- `PaymentApi`: For API calls
- `WalletChargeDto`: Data model for payment requests

## Class Structure

```dart
final paymentProvider =
    StateNotifierProvider<PaymentProvider, PaymentState>((ref) {
  return PaymentProvider(
    paymentService: sl<PaymentService>(),
    paymentApi: sl<PaymentApi>(),
    ref: ref,
  );
});

class PaymentState {
  final bool isLoading;
  final String? error;

  PaymentState({
    this.isLoading = false,
    this.error,
  });

  PaymentState copyWith({
    bool? isLoading,
    String? error,
  });
}

class PaymentProvider extends StateNotifier<PaymentState> {
  final PaymentService paymentService;
  final PaymentApi paymentApi;
  final Ref ref;

  PaymentProvider({
    required this.paymentService,
    required this.paymentApi,
    required this.ref,
  }) : super(PaymentState());

  Future<void> processCardPayment({...});
  Future<void> processApplePayPayment({...});
  Future<void> _handlePaymentResult({...});
}
```

## Key Methods

### processCardPayment

Processes a card payment:

```dart
Future<void> processCardPayment({
  required BuildContext context,
  required WalletChargeDto chargeDto,
}) async {
  state = state.copyWith(isLoading: true);
  
  try {
    // Calculate VAT and total amount
    final vatAmount = chargeDto.amountPrice * 0.15;
    final totalAmount = chargeDto.amountPrice + vatAmount;
    
    // Start card payment
    final result = await paymentService.startCardPayment(
      amount: totalAmount,
      currency: "SAR",
      cartDescription: "Purchase ${chargeDto.amountPoint} points",
      chargeDto: chargeDto,
    );
    
    // Check if the context is still valid
    if (context.mounted) {
      // Handle payment result
      _handlePaymentResult(
        context, 
        result, 
        chargeDto.copyWith(
          vatAmount: vatAmount,
          totalAmount: totalAmount,
          paymentMethod: 'card',
        ),
      );
    }
  } catch (e) {
    state = state.copyWith(isLoading: false, error: e.toString());
    NotificationAlert.showLocalNotification(
      e.toString(),
      type: ToastType.error,
    );
  }
}
```

### processApplePayPayment

Processes an Apple Pay payment:

```dart
Future<void> processApplePayPayment({
  required BuildContext context,
  required WalletChargeDto chargeDto,
}) async {
  state = state.copyWith(isLoading: true);
  
  try {
    // Calculate VAT and total amount
    final vatAmount = chargeDto.amountPrice * 0.15;
    final totalAmount = chargeDto.amountPrice + vatAmount;
    
    // Start Apple Pay payment
    final result = await paymentService.startApplePayPayment(
      amount: totalAmount,
      currency: "SAR",
      cartDescription: "Purchase ${chargeDto.amountPoint} points",
      chargeDto: chargeDto,
    );
    
    // Check if the context is still valid
    if (context.mounted) {
      // Handle payment result
      _handlePaymentResult(
        context, 
        result, 
        chargeDto.copyWith(
          vatAmount: vatAmount,
          totalAmount: totalAmount,
          paymentMethod: 'apple_pay',
        ),
      );
    }
  } catch (e) {
    state = state.copyWith(isLoading: false, error: e.toString());
    NotificationAlert.showLocalNotification(
      e.toString(),
      type: ToastType.error,
    );
  }
}
```

### _handlePaymentResult

Handles the payment result and updates the user's wallet:

```dart
Future<void> _handlePaymentResult(
  BuildContext context,
  Map<String, dynamic> result,
  WalletChargeDto chargeDto,
) async {
  if (result["status"] == "success") {
    final transactionDetails = result["data"];

    if (transactionDetails["isSuccess"]) {
      // Payment successful, update wallet
      try {
        // Update chargeDto with payment reference
        final updatedChargeDto = chargeDto.copyWith(
          paymentReference: transactionDetails["transactionReference"],
        );

        // Call API to process payment
        final chargeUser = await paymentApi.processPayment(updatedChargeDto);

        if (chargeUser.meta.success ?? false) {
          // Success notification
          NotificationAlert.showLocalNotification(
            chargeUser.meta.message,
            type: ToastType.success,
            align: Alignment.topCenter,
          );

          // Refresh user data
          ref.refresh(meProvider);

          // Navigate back to wallet screen
          goRouter.pop();
          goRouter.pop(); // Pop payment method selection and charge wallet screens
        } else {
          // API error
          NotificationAlert.showLocalNotification(
            chargeUser.meta.message,
            type: ToastType.error,
          );
        }
      } catch (e) {
        // Error updating wallet
        NotificationAlert.showLocalNotification(
          e.toString(),
          type: ToastType.error,
        );
      }
    } else {
      // Payment failed
      NotificationAlert.showLocalNotification(
        context.localizations.payment_failed,
        type: ToastType.error,
      );
    }
  } else if (result["status"] == "error") {
    // Payment error
    NotificationAlert.showLocalNotification(
      result["message"] ?? context.localizations.payment_error,
      type: ToastType.error,
    );
  } else if (result["status"] == "event") {
    // Payment cancelled
    NotificationAlert.showLocalNotification(
      context.localizations.payment_cancelled,
      type: ToastType.warning,
    );
  }

  state = state.copyWith(isLoading: false);
}
```

## Usage Example

```dart
// In PaymentMethodSelectionView
Widget build(BuildContext context, WidgetRef ref) {
  final paymentState = ref.watch(paymentProvider);
  
  return Scaffold(
    // ...
    body: paymentState.isLoading
      ? Center(child: CircularProgressIndicator())
      : Column(
          children: [
            // Apple Pay button
            _buildPaymentButton(
              context,
              child: SvgPicture.asset(AppIcons.applePay),
              onTap: () => ref.read(paymentProvider.notifier).processApplePayPayment(
                context: context,
                chargeDto: chargeDto,
              ),
            ),
            
            // Credit/Debit Card button
            _buildPaymentButton(
              context,
              child: Row(children: [
                SvgPicture.asset(AppIcons.creditCard),
                Text(context.localizations.credit_debit_card),
              ]),
              onTap: () => ref.read(paymentProvider.notifier).processCardPayment(
                context: context,
                chargeDto: chargeDto,
              ),
            ),
          ],
        ),
  );
}
```
