<svg width="361" height="246" viewBox="0 0 361 246" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3067_109637)">
<rect width="361" height="246" rx="16" fill="url(#paint0_linear_3067_109637)"/>
<g opacity="0.22">
<mask id="mask0_3067_109637" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="-96" width="336" height="336">
<rect width="336" height="336" transform="translate(12 -96)" fill="url(#paint1_radial_3067_109637)"/>
</mask>
<g mask="url(#mask0_3067_109637)">
<circle cx="180" cy="72" r="47.5" stroke="#FFC34F"/>
<circle cx="180" cy="72" r="47.5" stroke="#FFC34F"/>
<circle cx="180" cy="72" r="71.5" stroke="#FFC34F"/>
<circle cx="180" cy="72" r="95.5" stroke="#FFC34F"/>
<circle cx="180" cy="72" r="119.5" stroke="#FFC34F"/>
<circle cx="180" cy="72" r="143.5" stroke="#FFC34F"/>
<circle cx="180" cy="72" r="167.5" stroke="#FFC34F"/>
</g>
</g>
<g filter="url(#filter0_f_3067_109637)">
<circle cx="181" cy="72" r="42" fill="#FFCC29" fill-opacity="0.18"/>
</g>
<circle cx="181.5" cy="71.5" r="35.5" fill="#FFCC29" fill-opacity="0.18"/>
<g filter="url(#filter1_f_3067_109637)">
<path d="M110.5 33L113.306 41.6373H122.388L115.041 46.9754L117.847 55.6127L110.5 50.2746L103.153 55.6127L105.959 46.9754L98.6118 41.6373H107.694L110.5 33Z" fill="#FACC15"/>
</g>
<g filter="url(#filter2_f_3067_109637)">
<path d="M246.5 45L247.735 48.8004H251.731L248.498 51.1492L249.733 54.9496L246.5 52.6008L243.267 54.9496L244.502 51.1492L241.269 48.8004H245.265L246.5 45Z" fill="#FACC15"/>
</g>
<path d="M241.5 104L242.286 106.418H244.829L242.771 107.913L243.557 110.332L241.5 108.837L239.443 110.332L240.229 107.913L238.171 106.418H240.714L241.5 104Z" fill="#FACC15"/>
</g>
<rect x="0.5" y="0.5" width="360" height="245" rx="15.5" stroke="#FEF08A"/>
<defs>
<filter id="filter0_f_3067_109637" x="135" y="26" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_3067_109637"/>
</filter>
<filter id="filter1_f_3067_109637" x="94.6118" y="29" width="31.7764" height="30.6128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_3067_109637"/>
</filter>
<filter id="filter2_f_3067_109637" x="237.269" y="41" width="18.4614" height="17.9497" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_3067_109637"/>
</filter>
<linearGradient id="paint0_linear_3067_109637" x1="236" y1="293.074" x2="-595.154" y2="251.578" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF8E1"/>
<stop offset="1" stop-color="#FFCC29"/>
</linearGradient>
<radialGradient id="paint1_radial_3067_109637" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(168 168) rotate(90) scale(168 168)">
<stop/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_3067_109637">
<rect width="361" height="246" rx="16" fill="white"/>
</clipPath>
</defs>
</svg>
