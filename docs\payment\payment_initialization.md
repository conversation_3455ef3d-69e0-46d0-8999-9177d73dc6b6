# PaymentInitialization

The `PaymentInitialization` class is a data model for the response from the payment initialization API.

## Location

`lib/features/payment/data/models/payment_initialization.dart`

## Class Structure

```dart
class PaymentInitialization extends Equatable {
  final bool success;
  final String transactionId;
  final Map<String, dynamic> paymentData;

  const PaymentInitialization({
    required this.success,
    required this.transactionId,
    required this.paymentData,
  });

  factory PaymentInitialization.fromJson(Map<String, dynamic> json);

  @override
  List<Object?> get props => [success, transactionId, paymentData];
}
```

## Properties

- `success`: Whether the initialization was successful
- `transactionId`: The ID of the transaction created on the backend
- `paymentData`: Additional payment data from the backend

## Methods

### from<PERSON><PERSON>

Creates a `PaymentInitialization` instance from a JSON map:

```dart
factory PaymentInitialization.fromJson(Map<String, dynamic> json) {
  return PaymentInitialization(
    success: json['success'] ?? false,
    transactionId: json['transaction_id'] ?? '',
    paymentData: json['payment_data'] ?? {},
  );
}
```

## API Response Example

```json
{
  "success": true,
  "transaction_id": "#TID-12345",
  "payment_data": {
    "tran_ref": "TST123456789"
  }
}
```

## Usage Example

```dart
// In PaymentApi
Future<PaymentInitialization> initializePayment(WalletChargeDto dto) => _bondFire
    .post<PaymentInitialization>('/payment/initialize')
    .body({
      'amount_price': dto.amountPrice,
      'amount_point': dto.amountPoint,
    })
    .header(Api.headers())
    .factory(PaymentInitialization.fromJson)
    .errorFactory(ServerError.fromJson)
    .execute();

// In PaymentService
Future<Map<String, dynamic>> startCardPayment({
  required double amount,
  required String currency,
  required String cartDescription,
  required WalletChargeDto chargeDto,
}) async {
  // Initialize payment with backend
  final initialization = await paymentApi.initializePayment(chargeDto);
  
  // Use the transaction ID for PayTabs SDK
  final configuration = _createBaseConfiguration(
    amount: amount,
    currency: currency,
    cartDescription: cartDescription,
    cartId: initialization.transactionId,
    billingDetails: _createBillingDetails(),
  );
  
  // ...
}
```
