<!--
Thank you for contributing to Flutter Bond! Before submitting your issue, please ensure that you have read our guidelines and filled out the necessary details. 

- If you're reporting a bug, please provide a clear and concise description along with steps to reproduce it.
- If you're suggesting an enhancement or feature request, please explain the motivation and use-case for it.

Please delete this comment section and provide the required information below.
-->

## Description
<!-- Provide a clear and concise description of the issue or suggestion. -->

## Steps to Reproduce
<!-- If you're reporting a bug, please provide steps to reproduce it. -->

1. Step 1
2. Step 2
3. ...

## Expected Behavior
<!-- If you're reporting a bug, describe what you expected to happen. -->

## Actual Behavior
<!-- If you're reporting a bug, describe what actually happened. -->

## Screenshots/Code Snippets
<!-- If applicable, provide any relevant screenshots or code snippets that can help understand the issue. -->

## Version Info
Please paste the output of running `flutter doctor -v` here (available from the command
line or from `Tools > Flutter > Flutter Doctor`). It will provide the version of the
Flutter framework as well as of the IntelliJ plugin.
|                         |                           |
|-------------------------|---------------------------|
| Flutter version:         |                         |
| Android toolchain:       |                         |
| Xcode for iOS/macOS:     |                         |
| Chrome for web:          |                         |
| Android Studio:          |                         |
| VS Code:                 |                         |
| Connected devices:       |                         |
| Issues summary:          |                         |

## Related Issues/PRs
<!-- If there are any related issues or pull requests, mention them here. -->

## Possible Solution
<!-- If you have any ideas or suggestions for a possible solution, please mention them. -->

## Additional Information
<!-- Provide any additional information or screenshots that might be helpful in understanding the issue. -->

<!-- Thanks for contributing to Flutter Bond! We appreciate your time and effort. -->
