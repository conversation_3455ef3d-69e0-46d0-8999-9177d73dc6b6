<?php

// app/Services/AI/OpenAIService.php
namespace App\Services\AI;

use App\Actions\OpenAI\TrackOpenAIUsageAction;
use App\Services\Langfuse\LangfuseTrackingService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use OpenAI\Client;
use OpenAI\Laravel\Facades\OpenAI;

class OpenAIService implements AIServiceInterface
{
    protected $model;
    protected $trackOpenAIUsageAction;
    protected $client;
    protected $langfuseTracking;

    public function __construct(string $model = 'gpt-4')
    {
        $this->model = $model;
        $this->trackOpenAIUsageAction = App::make(TrackOpenAIUsageAction::class);
        $this->langfuseTracking = App::make(LangfuseTrackingService::class);
        $this->client = $this->createClient();
    }

    /**
     * Create OpenAI client with optional Helicone configuration
     */
    protected function createClient(): Client
    {
        $factory = \OpenAI::factory()
            ->withApi<PERSON>ey(config('openai.api_key'));

        // Add organization if configured
        if (config('openai.organization')) {
            $factory = $factory->withOrganization(config('openai.organization'));
        }

        // Configure Helicone if enabled
        if (config('openai.helicone.enabled', false)) {
            $heliconeApiKey = config('openai.helicone.api_key');
            $heliconeBaseUrl = config('openai.helicone.base_url', 'https://oai.helicone.ai/v1');

            if ($heliconeApiKey) {
                $factory = $factory
                    ->withBaseUri($heliconeBaseUrl)
                    ->withHttpHeader('Helicone-Auth', 'Bearer ' . $heliconeApiKey);
            }
        }

        return $factory->make();
    }

    public function generateResponse(string $prompt, array $options = []): string
    {
        $model = $options['model'] ?? $this->model;
        $source = $options['source'] ?? 'OpenAIService';
        $action = $options['action'] ?? 'generate_response';
        $userId = $options['user_id'] ?? null;
        $chatMessageId = $options['chat_message_id'] ?? null;
        $chatSessionId = $options['chat_session_id'] ?? null;

        $response = $this->client->chat()->create([
            'model' => $model,
            'messages' => [
                ['role' => 'user', 'content' => $prompt],
            ],
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
        ]);

        // Track API usage
        try {
            $responseArray = json_decode(json_encode($response->toArray()), true);

            $metadata = [
                'prompt_length' => strlen($prompt),
                'response_length' => strlen($responseArray['choices'][0]['message']['content'] ?? ''),
                'request_type' => 'chat',
                'endpoint' => '/v1/chat/completions',
            ];

            // Add any additional metadata from options
            if (isset($options['metadata']) && is_array($options['metadata'])) {
                $metadata = array_merge($metadata, $options['metadata']);
            }

            $this->trackOpenAIUsageAction->execute(
                $model,
                $responseArray,
                $metadata,
                $userId,
                $chatMessageId,
                $source,
                $action,
                $chatSessionId
            );

            // Track in Langfuse if chat message is available
            $this->trackInLangfuse($chatMessageId, $prompt, $responseArray);
        } catch (\Exception $e) {
            Log::error('Failed to track OpenAI API usage', [
                'error' => $e->getMessage(),
                'model' => $model,
                'source' => $source,
            ]);
        }

        return $response->choices[0]->message->content;
    }

    public function streamResponse(string $prompt, callable $callback, array $options = []): void
    {
        $model = $options['model'] ?? $this->model;
        $source = $options['source'] ?? 'OpenAIService';
        $action = $options['action'] ?? 'stream_response';
        $userId = $options['user_id'] ?? null;
        $chatMessageId = $options['chat_message_id'] ?? null;
        $chatSessionId = $options['chat_session_id'] ?? null;

        $stream = $this->client->chat()->createStreamed([
            'model' => $model,
            'messages' => [
                ['role' => 'user', 'content' => $prompt],
            ],
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
        ]);

        $fullResponse = '';
        $lastChunk = null;

        foreach ($stream as $response) {
            if (isset($response->choices[0]->delta->content)) {
                $chunk = $response->choices[0]->delta->content;
                $fullResponse .= $chunk;
                $callback($chunk, $fullResponse);
                $lastChunk = $response;
            }
        }

        // Track API usage after streaming is complete
        try {
            // For streaming responses, we don't get usage stats directly
            // We'll estimate based on the length of the prompt and response
            $metadata = [
                'prompt_length' => strlen($prompt),
                'response_length' => strlen($fullResponse),
                'is_streamed' => true,
                'request_type' => 'chat_stream',
                'endpoint' => '/v1/chat/completions',
            ];

            // Add any additional metadata from options
            if (isset($options['metadata']) && is_array($options['metadata'])) {
                $metadata = array_merge($metadata, $options['metadata']);
            }

            // Estimate token counts (rough approximation: ~4 chars per token)
            $promptTokens = ceil(strlen($prompt) / 4);
            $completionTokens = ceil(strlen($fullResponse) / 4);

            // Create a response array with estimated token usage
            $responseArray = [
                'usage' => [
                    'prompt_tokens' => $promptTokens,
                    'completion_tokens' => $completionTokens,
                    'total_tokens' => $promptTokens + $completionTokens,
                ]
            ];

            $this->trackOpenAIUsageAction->execute(
                $model,
                $responseArray,
                $metadata,
                $userId,
                $chatMessageId,
                $source,
                $action,
                $chatSessionId
            );

            // Track in Langfuse if chat message is available
            $this->trackInLangfuse($chatMessageId, $prompt, $responseArray);
        } catch (\Exception $e) {
            Log::error('Failed to track OpenAI API streaming usage', [
                'error' => $e->getMessage(),
                'model' => $model,
                'source' => $source,
            ]);
        }
    }

    /**
     * Track OpenAI call in Langfuse if chat message is available
     */
    protected function trackInLangfuse(?int $chatMessageId, string $prompt, array $responseArray): void
    {
        if (!$chatMessageId) {
            return;
        }

        try {
            $chatMessage = \App\Models\ChatMessage::find($chatMessageId);
            if ($chatMessage) {
                $this->langfuseTracking->trackChatMessage(
                    $chatMessage,
                    $prompt,
                    $responseArray
                );
            }
        } catch (\Exception $e) {
            Log::warning('Failed to track in Langfuse', [
                'chat_message_id' => $chatMessageId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
