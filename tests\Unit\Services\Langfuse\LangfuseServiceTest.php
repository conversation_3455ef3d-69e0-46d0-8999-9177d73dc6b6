<?php

namespace Tests\Unit\Services\Langfuse;

use App\Services\Langfuse\LangfuseService;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LangfuseServiceTest extends TestCase
{
    use RefreshDatabase;

    protected LangfuseService $langfuseService;
    protected MockHandler $mockHandler;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the HTTP client
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);

        // Set up test configuration
        config([
            'langfuse.enabled' => true,
            'langfuse.public_key' => 'test_public_key',
            'langfuse.secret_key' => 'test_secret_key',
            'langfuse.host' => 'https://test.langfuse.com',
            'langfuse.timeout' => 30,
            'langfuse.connect_timeout' => 10,
        ]);

        $this->langfuseService = new LangfuseService();

        // Replace the client with our mocked version
        $reflection = new \ReflectionClass($this->langfuseService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->langfuseService, new Client(['handler' => $handlerStack]));
    }

    public function test_is_enabled_returns_true_when_properly_configured()
    {
        $this->assertTrue($this->langfuseService->isEnabled());
    }

    public function test_is_enabled_returns_false_when_disabled()
    {
        config(['langfuse.enabled' => false]);
        $service = new LangfuseService();

        $this->assertFalse($service->isEnabled());
    }

    public function test_is_enabled_returns_false_when_missing_credentials()
    {
        config(['langfuse.public_key' => null]);
        $service = new LangfuseService();

        $this->assertFalse($service->isEnabled());
    }

    public function test_create_trace_returns_trace_id_on_success()
    {
        $this->mockHandler->append(new Response(200, [], json_encode(['id' => 'trace_123'])));

        $traceData = [
            'name' => 'Test Trace',
            'userId' => '1',
            'sessionId' => 'session_123',
            'metadata' => ['test' => 'data'],
        ];

        $traceId = $this->langfuseService->createTrace($traceData);

        $this->assertNotNull($traceId);
        $this->assertIsString($traceId);
    }

    public function test_create_trace_returns_null_when_disabled()
    {
        config(['langfuse.enabled' => false]);
        $service = new LangfuseService();

        $traceId = $service->createTrace(['name' => 'Test']);

        $this->assertNull($traceId);
    }

    public function test_create_generation_returns_generation_id_on_success()
    {
        $this->mockHandler->append(new Response(200, [], json_encode(['id' => 'gen_123'])));

        $generationData = [
            'traceId' => 'trace_123',
            'name' => 'Test Generation',
            'model' => 'gpt-4',
            'input' => 'Test prompt',
            'output' => 'Test response',
            'usage' => [
                'promptTokens' => 10,
                'completionTokens' => 20,
                'totalTokens' => 30,
            ],
        ];

        $generationId = $this->langfuseService->createGeneration($generationData);

        $this->assertNotNull($generationId);
        $this->assertIsString($generationId);
    }

    public function test_create_session_returns_session_id_on_success()
    {
        $this->mockHandler->append(new Response(200, [], json_encode(['id' => 'session_123'])));

        $sessionData = [
            'userId' => '1',
            'metadata' => ['agent' => 'test_agent'],
        ];

        $sessionId = $this->langfuseService->createSession($sessionData);

        $this->assertNotNull($sessionId);
        $this->assertIsString($sessionId);
    }

    public function test_create_score_returns_true_on_success()
    {
        $this->mockHandler->append(new Response(200, [], json_encode(['id' => 'score_123'])));

        $scoreData = [
            'traceId' => 'trace_123',
            'name' => 'user_rating',
            'value' => 5,
            'comment' => 'Great response!',
        ];

        $result = $this->langfuseService->createScore($scoreData);

        $this->assertTrue($result);
    }

    public function test_content_truncation_works_correctly()
    {
        $longContent = str_repeat('a', 15000);

        config(['langfuse.tracing.max_content_length' => 1000]);
        $service = new LangfuseService();

        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('truncateContent');
        $method->setAccessible(true);

        $result = $method->invoke($service, $longContent);

        $this->assertStringEndsWith('... [truncated]', $result);
        $this->assertLessThanOrEqual(1015, strlen($result)); // 1000 + "... [truncated]"
    }

    public function test_error_handling_logs_errors_when_configured()
    {
        config([
            'langfuse.error_handling.log_errors' => true,
            'langfuse.error_handling.fail_silently' => true,
        ]);

        $this->mockHandler->append(new Response(500, [], 'Server Error'));

        $traceId = $this->langfuseService->createTrace(['name' => 'Test']);

        $this->assertNull($traceId);
        // In a real test, you would assert that the error was logged
    }

    public function test_handles_network_errors_gracefully()
    {
        config([
            'langfuse.error_handling.fail_silently' => true,
        ]);

        // Simulate network error
        $this->mockHandler->append(new \GuzzleHttp\Exception\ConnectException(
            'Connection failed',
            new \GuzzleHttp\Psr7\Request('POST', 'test')
        ));

        $traceId = $this->langfuseService->createTrace(['name' => 'Test']);

        $this->assertNull($traceId);
    }
}
