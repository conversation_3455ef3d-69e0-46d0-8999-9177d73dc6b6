# WalletChargeDto

The `WalletChargeDto` class is a data transfer object used for wallet charging operations.

## Location

`lib/features/wallet/data/models/wallet_charge_dto.dart`

## Class Structure

```dart
class WalletChargeDto {
  final double amountPrice;
  final int amountPoint;
  final double? vatAmount;
  final double? totalAmount;
  final String? paymentReference;
  final String? paymentMethod;

  WalletChargeDto(
    this.amountPrice,
    this.amountPoint, {
    this.vatAmount,
    this.totalAmount,
    this.paymentReference,
    this.paymentMethod,
  });

  Map<String, dynamic> toJson();

  WalletChargeDto copyWith({
    double? amountPrice,
    int? amountPoint,
    double? vatAmount,
    double? totalAmount,
    String? paymentReference,
    String? paymentMethod,
  });
}
```

## Properties

- `amountPrice`: The price amount in SAR
- `amountPoint`: The points amount to purchase
- `vatAmount`: The VAT amount (15% of amountPrice)
- `totalAmount`: The total amount including VAT
- `paymentReference`: The payment reference from PayTabs
- `paymentMethod`: The payment method used (e.g., 'card', 'apple_pay')

## Methods

### toJson

Converts the DTO to a JSON map:

```dart
Map<String, dynamic> toJson() {
  return {
    'amount_price': amountPrice,
    'amount_point': amountPoint,
    'vat_amount': vatAmount,
    'total_amount': totalAmount,
    'payment_reference': paymentReference,
    'payment_method': paymentMethod,
  };
}
```

### copyWith

Creates a copy of the DTO with optional new values:

```dart
WalletChargeDto copyWith({
  double? amountPrice,
  int? amountPoint,
  double? vatAmount,
  double? totalAmount,
  String? paymentReference,
  String? paymentMethod,
}) {
  return WalletChargeDto(
    amountPrice ?? this.amountPrice,
    amountPoint ?? this.amountPoint,
    vatAmount: vatAmount ?? this.vatAmount,
    totalAmount: totalAmount ?? this.totalAmount,
    paymentReference: paymentReference ?? this.paymentReference,
    paymentMethod: paymentMethod ?? this.paymentMethod,
  );
}
```

## Usage Example

```dart
// Creating a new DTO
WalletChargeDto chargeDto = WalletChargeDto(100.0, 1000);

// Calculating VAT and total amount
final vatAmount = chargeDto.amountPrice * 0.15;
final totalAmount = chargeDto.amountPrice + vatAmount;

// Updating the DTO with payment details
final updatedChargeDto = chargeDto.copyWith(
  vatAmount: vatAmount,
  totalAmount: totalAmount,
  paymentReference: "TST123456789",
  paymentMethod: "card",
);

// Converting to JSON for API request
final json = updatedChargeDto.toJson();
```

## API Request Example

```json
{
  "amount_price": 100,
  "amount_point": 1000,
  "vat_amount": 15,
  "total_amount": 115,
  "payment_reference": "TST123456789",
  "payment_method": "card"
}
```
