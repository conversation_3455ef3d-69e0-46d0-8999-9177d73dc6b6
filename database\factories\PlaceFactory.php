<?php

namespace Database\Factories;

use App\Models\Agent;
use App\Models\Category;
use App\Models\Place;
use App\Models\Subcategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Place>
 */
class PlaceFactory extends Factory
{
    protected $model = Place::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => [
                'en' => $this->faker->company(),
                'ar' => $this->faker->company(),
            ],
            'description' => [
                'en' => $this->faker->paragraph(),
                'ar' => $this->faker->paragraph(),
            ],
            'brief_description' => $this->faker->sentence(),
            'address' => [
                'en' => $this->faker->address(),
                'ar' => $this->faker->address(),
            ],
            'latitude' => $this->faker->latitude(25.0, 25.4),
            'longitude' => $this->faker->longitude(55.0, 55.5),
            'rating' => $this->faker->randomFloat(1, 3.0, 5.0),
            'place_id' => 'place_id_' . $this->faker->unique()->uuid(),
            'category_id' => Category::factory(),
            'agent_id' => Agent::factory(),
            'is_active' => true,
            'is_choice' => false,
            'is_trending' => false,
            'source' => 'manual',
        ];
    }

    /**
     * Configure the place as a choice place.
     */
    public function choice(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'is_choice' => true,
            ];
        });
    }

    /**
     * Configure the place as a trending place.
     */
    public function trending(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'is_trending' => true,
            ];
        });
    }

    /**
     * Configure the place as inactive.
     */
    public function inactive(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}
