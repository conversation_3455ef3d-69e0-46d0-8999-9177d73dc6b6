<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FeatureUsageStatistic extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'feature_type',
        'usage_count',
        'date'
    ];

    protected $casts = [
        'date' => 'date',
        'usage_count' => 'integer'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function incrementUsage($userId, $featureType)
    {
        $date = now()->toDateString();

        return self::updateOrCreate(
            [
                'user_id' => $userId,
                'feature_type' => $featureType,
                'date' => $date
            ],
            [
                'usage_count' => \DB::raw('usage_count + 1')
            ]
        );
    }
}
