{"version": "0.2.0", "configurations": [{"name": "Flutter: Run production", "request": "launch", "type": "dart", "program": "${workspaceFolder}/lib/main.dart", "args": ["--flavor", "Production", "--dart-define-from-file=env.json"]}, {"name": "Flutter: Run staging", "request": "launch", "type": "dart", "program": "${workspaceFolder}/lib/main.dart", "args": ["--flavor", "Staging", "--dart-define-from-file=env.staging.json"]}]}