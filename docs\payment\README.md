# PayTabs Integration Documentation

This directory contains detailed documentation for the PayTabs integration in the Zod mobile app.

## Components

### Core Components

- [**PaymentService**](payment_service.md): Handles interaction with the PayTabs SDK
- [**PaymentApi**](payment_api.md): Handles API calls to the backend
- [**PaymentProvider**](payment_provider.md): Manages payment state and operations
- [**PaymentMethodSelectionView**](payment_method_selection_view.md): Bottom sheet UI for selecting payment method

### Data Models

- [**WalletChargeDto**](wallet_charge_dto.md): Data transfer object for wallet charging operations
- [**PaymentInitialization**](payment_initialization.md): Data model for payment initialization response

### Related Components

- [**ChargeWalletProvider**](charge_wallet_provider.md): Manages wallet charging state and operations

## Implementation Details

The PayTabs integration follows a clean architecture approach with separation of concerns and uses Riverpod tool for state management:

1. **Presentation Layer**: UI components and state management

   - `PaymentMethodSelectionView`: Bottom sheet for payment method selection
   - `PaymentProvider`: State management using Riverpod

2. **Domain Layer**: Business logic and models

   - `PaymentService`: Handles payment processing logic
   - `WalletChargeDto`: Data model for payment requests

3. **Data Layer**: API communication and data sources
   - `PaymentApi`: Handles API calls to the backend
   - `PaymentInitialization`: Data model for initialization response

## Riverpod 


- **State Management**: Managing the payment state and UI updates
- **Dependency Injection**: Providing dependencies to widgets and other providers
- **Reactivity**: Automatically rebuilding the UI when the state changes
- **Testability**: Making the code more testable with clear separation of concerns

The main providers used in this implementation are:

- `paymentProvider`: Manages the payment state and operations
- `chargeWalletProvider`: Manages the wallet charging state and operations

Example of a provider definition:

```dart
final paymentProvider =
    StateNotifierProvider<PaymentProvider, PaymentState>((ref) {
  return PaymentProvider(
    paymentService: sl<PaymentService>(),
    paymentApi: sl<PaymentApi>(),
    ref: ref,
  );
});
```

## Payment Flow

The payment flow follows these steps:

1. **Wallet Charge Initiation**:

   - User selects points amount in the Charge Wallet screen
   - User taps "Buy Points" button
   - `ChargeWalletProvider.chargeWallet()` is called
   - Payment Method Selection bottom sheet appears

2. **Payment Method Selection**:

   - User selects payment method (Apple Pay or Credit/Debit Card)
   - `PaymentProvider.processCardPayment()` or `processApplePayPayment()` is called

3. **Payment Initialization**:

   - App calls `/payment/initialize` endpoint via `PaymentApi.initializePayment()`
   - Backend creates a transaction record and returns transaction ID
   - App configures PayTabs SDK with the transaction ID

4. **Payment Processing**:

   - PayTabs SDK handles the payment UI and processing
   - User enters payment details or confirms Apple Pay
   - PayTabs processes the payment and returns result

5. **Payment Completion**:
   - On successful payment, app calls `/payment/process` endpoint via `PaymentApi.processPayment()`
   - Backend verifies payment and updates user's wallet
   - App shows success message and refreshes wallet balance
   - Bottom sheet is dismissed

## Screenshots

See the [images directory](../images) for screenshots of the PayTabs integration.

## PayTabs  

This implementation uses PayTabs'  which provides a unified payment experience across multiple channels.l is integrated through the `flutter_paytabs_bridge` package and configured in the `PaymentService` class.

Key features of the :

- **Multiple Payment Methods**: Support for credit/debit cards, Apple Pay, and other payment methods
- **Seamless Integration**: Easy integration with mobile apps using the Flutter PayTabs Bridge package
- **Secure Transactions**: PCI-DSS compliant payment processing
- **Customizable UI**: Ability to customize the payment UI to match the app's design
- **Real-time Notifications**: Instant notifications for payment events
- **Comprehensive Reporting**: Detailed reporting and analytics for transactions

For more details on the  tool configuration, see the [PaymentService documentation](payment_service.md).

## References

- [Main Documentation](../../README_PAYTABS_INTEGRATION.md)
- [PayTabs Documentation](https://site.paytabs.com/en/developers/)
- [PayTabs  Documentation](https://site.paytabs.com/en/)
- [Flutter PayTabs Bridge Package](https://pub.dev/packages/flutter_paytabs_bridge)
