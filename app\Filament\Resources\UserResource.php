<?php

namespace App\Filament\Resources;

use App\Enums\UserType;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers\DeviceInfoRelationManager;
use App\Models\User;
use App\Models\SystemSetting;
use App\Models\UserRecommendationUsage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Toggle::make('is_demo')
                    ->required(),
                Forms\Components\Toggle::make('is_anonymous')
                    ->required(),
                Forms\Components\Toggle::make('is_first_login')
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('password')
                    ->password()
                    ->required()
                    ->maxLength(255)
                    ->hiddenOn('edit'),
                Forms\Components\Select::make('user_type')
                    ->options(UserType::class)
                    ->default(UserType::User)
                    ->required(),
                Forms\Components\DateTimePicker::make('next_points_renewal_at')
                    ->hiddenOn(['create']),
                Forms\Components\DateTimePicker::make('email_verified_at')
                    ->hiddenOn(['create']),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('points')
                    ->label('Points')
                    ->sortable()
                    ->formatStateUsing(function ($record) {
                        return $record->getPoints();
                    })
                    ->toggleable(),

                //next_points_renewal_at
                Tables\Columns\TextColumn::make('next_points_renewal_at')
                    ->label('Next Renewal')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('user_type')
                    ->badge()
                    ->formatStateUsing(function ($record) {
                        if ($record->is_anonymous) {
                            return 'Guest';
                        }
                        if ($record->subscriptions()->where('status', 'active')->exists()) {
                            return 'Rydo+';
                        }
                        return 'Registered';
                    })
                    ->color(function ($record) {
                        if ($record->is_anonymous) {
                            return 'gray';
                        }
                        if ($record->subscriptions()->where('status', 'active')->exists()) {
                            return 'success';
                        }
                        return 'info';
                    }),
                Tables\Columns\TextColumn::make('socialProviders.provider')
                    ->label('Sign-up Method')
                    ->formatStateUsing(fn($state) => ucfirst($state ?? 'Email'))
                    ->listWithLineBreaks(),
                Tables\Columns\IconColumn::make('email_verified_at')
                    ->boolean()
                    ->label('Verified')
                    ->trueIcon('heroicon-o-check-badge')
                    ->falseIcon('heroicon-o-x-mark'),
                Tables\Columns\IconColumn::make('is_first_login')
                    ->boolean()
                    ->label('First Login')
                    ->trueIcon('heroicon-o-sparkles')
                    ->falseIcon('heroicon-o-user'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Joined')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('subscriptions.status')
                    ->label('Status')
                    ->formatStateUsing(function ($state) {
                        if (!$state) {
                            return 'No Subscription';
                        }
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(function ($state) {
                        if (!$state) {
                            return 'gray';
                        }
                        return $state->getColor();
                    }),
                Tables\Columns\TextColumn::make('recommendation_usage')
                    ->label('Recommendations')
                    ->formatStateUsing(function ($record) {
                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();

                        if (!$usage) {
                            return '0 / ' . SystemSetting::getValue(
                                SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                                10
                            );
                        }

                        // Check if we need to reset the count
                        if ($usage->next_reset_at && $usage->next_reset_at->isPast()) {
                            return '0 / ' . SystemSetting::getValue(
                                SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                                10
                            ) . ' (Reset)';
                        }

                        return $usage->count . ' / ' . SystemSetting::getValue(
                            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                            10
                        );
                    })
                    ->badge()
                    ->color(function ($record) {
                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();

                        if (!$usage) {
                            return 'success';
                        }

                        // Check if we need to reset the count
                        if ($usage->next_reset_at && $usage->next_reset_at->isPast()) {
                            return 'success';
                        }

                        $limit = SystemSetting::getValue(
                            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                            10
                        );

                        if ($usage->count >= $limit) {
                            return 'danger';
                        }

                        if ($usage->count >= ($limit * 0.8)) {
                            return 'warning';
                        }

                        return 'success';
                    })
                    ->toggleable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                DateRangeFilter::make('created_at'),
                Tables\Filters\SelectFilter::make('is_first_login')
                    ->options([
                        'true' => 'First Login',
                        'false' => 'Not First Login',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        return match ($data['value']) {
                            'true' => $query->where('is_first_login', true),
                            'false' => $query->where('is_first_login', false),
                        };
                    }),
                Tables\Filters\SelectFilter::make('user_type')
                    ->options([
                        'guest' => 'Guest',
                        'registered' => 'Registered',
                        'rydo_plus' => 'Rydo+',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        return match ($data['value']) {
                            'guest' => $query->where('is_anonymous', true),
                            'registered' => $query->where('is_anonymous', false)
                                ->whereDoesntHave('subscriptions', function ($query) {
                                    $query->where('status', 'active');
                                }),
                            'rydo_plus' => $query->whereHas('subscriptions', function ($query) {
                                $query->where('status', 'active');
                            }),
                            default => $query,
                        };
                    }),
                Tables\Filters\SelectFilter::make('sign_up_method')
                    ->options([
                        'google' => 'Google',
                        'apple' => 'Apple',
                        'email' => 'Email',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        if ($data['value'] === 'email') {
                            return $query->whereDoesntHave('socialProviders');
                        }

                        return $query->whereHas('socialProviders', function ($query) use ($data) {
                            $query->where('provider', $data['value']);
                        });
                    }),
                Tables\Filters\TernaryFilter::make('email_verified_at')
                    ->label('Email Verified'),
                Tables\Filters\SelectFilter::make('recommendation_limit')
                    ->label('Recommendation Usage')
                    ->options([
                        'reached' => 'Reached Limit',
                        'near' => 'Near Limit (≥80%)',
                        'available' => 'Available (<80%)',
                        'none' => 'No Usage Record',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        $limit = SystemSetting::getValue(
                            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                            10
                        );

                        return match ($data['value']) {
                            'reached' => $query->whereHas('recommendationUsage', function ($query) use ($limit) {
                                $query->where('count', '>=', $limit);
                            }),
                            'near' => $query->whereHas('recommendationUsage', function ($query) use ($limit) {
                                $query->where('count', '>=', $limit * 0.8)
                                      ->where('count', '<', $limit);
                            }),
                            'available' => $query->whereHas('recommendationUsage', function ($query) use ($limit) {
                                $query->where('count', '<', $limit * 0.8);
                            }),
                            'none' => $query->whereDoesntHave('recommendationUsage'),
                            default => $query,
                        };
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Action::make('editRecommendationLimits')
                    ->label('Edit Recommendation Limits')
                    ->icon('heroicon-o-adjustments-horizontal')
                    ->color('primary')
                    ->form(function (User $record) {
                        // Get the system default limit
                        $defaultLimit = SystemSetting::getValue(
                            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                            10
                        );

                        // Get the default renewal days
                        $defaultRenewalDays = SystemSetting::getValue(
                            SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                            7
                        );

                        // Get the user's recommendation usage record
                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();

                        return [
                            Forms\Components\Section::make('Edit Limits')
                                ->description('Modify the recommendation limits for this user')
                                ->schema([
                                    Forms\Components\TextInput::make('count')
                                        ->label('Usage Count')
                                        ->helperText('Set the current usage count')
                                        ->numeric()
                                        ->minValue(0)
                                        ->default(function () use ($usage) {
                                            return $usage ? $usage->count : 0;
                                        }),
                                    Forms\Components\DateTimePicker::make('next_reset_at')
                                        ->label('Next Reset Date')
                                        ->helperText('When the recommendation count will reset')
                                        ->default(function () use ($usage, $defaultRenewalDays) {
                                            if ($usage && $usage->next_reset_at) {
                                                return $usage->next_reset_at;
                                            }
                                            return now()->addDays($defaultRenewalDays);
                                        }),
                                ]),
                        ];
                    })
                    ->action(function (User $record, array $data) {
                        // Get or create the user's recommendation usage record
                        $usage = UserRecommendationUsage::firstOrNew(['user_id' => $record->id]);

                         // Update the count and next reset date
                        $usage->count = $data['count'];
                        $usage->next_reset_at = $data['next_reset_at'];

                        Notification::make()
                            ->title('Recommendation Limits Updated')
                            ->body("Recommendation limits for {$record->name} have been updated.")
                            ->success()
                            ->send();
                        $usage->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('bulkAddPoints')
                        ->label('Add Points to Selected')
                        ->icon('heroicon-o-plus-circle')
                        ->color('success')
                        ->form([
                            Forms\Components\TextInput::make('points')
                                ->label('Points to Add')
                                ->numeric()
                                ->minValue(1)
                                ->required(),
                            Forms\Components\Textarea::make('reason')
                                ->label('Reason')
                                ->required()
                                ->placeholder('Why are you adding these points?'),
                        ])
                        ->action(function ($records, array $data) {
                            $points = (int) $data['points'];
                            $reason = $data['reason'];
                            $count = $records->count();

                            foreach ($records as $record) {
                                $record->addSomePoints(
                                    $points,
                                    $reason,
                                    Auth::id()
                                );
                            }

                            Notification::make()
                                ->title('Points Added')
                                ->body("{$points} points have been added to {$count} users.")
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('bulkDeductPoints')
                        ->label('Deduct Points from Selected')
                        ->icon('heroicon-o-minus-circle')
                        ->color('danger')
                        ->form([
                            Forms\Components\TextInput::make('points')
                                ->label('Points to Deduct')
                                ->numeric()
                                ->minValue(1)
                                ->required(),
                            Forms\Components\Textarea::make('reason')
                                ->label('Reason')
                                ->required()
                                ->placeholder('Why are you deducting these points?'),
                        ])
                        ->action(function ($records, array $data) {
                            $points = (int) $data['points'];
                            $reason = $data['reason'];
                            $count = $records->count();
                            $skipped = 0;

                            foreach ($records as $record) {
                                // Skip users with insufficient points
                                if ($record->points < $points) {
                                    $skipped++;
                                    continue;
                                }

                                $record->deductSomePoints(
                                    $points,
                                    $reason,
                                    Auth::id()
                                );
                            }

                            $processedCount = $count - $skipped;

                            if ($skipped > 0) {
                                Notification::make()
                                    ->title('Points Deducted (Partially)')
                                    ->body("{$points} points have been deducted from {$processedCount} users. {$skipped} users were skipped due to insufficient points.")
                                    ->warning()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('Points Deducted')
                                    ->body("{$points} points have been deducted from {$count} users.")
                                    ->success()
                                    ->send();
                            }
                        }),
                    BulkAction::make('bulkResetPoints')
                        ->label('Reset Points for Selected')
                        ->icon('heroicon-o-arrow-path')
                        ->color('warning')
                        ->form([
                            Forms\Components\TextInput::make('points')
                                ->label('New Points Value')
                                ->numeric()
                                ->minValue(0)
                                ->required(),
                            Forms\Components\Textarea::make('reason')
                                ->label('Reason')
                                ->required()
                                ->placeholder('Why are you resetting these points?'),
                        ])
                        ->requiresConfirmation()
                        ->modalDescription('This will reset the points for all selected users to the specified value, overriding their current balances.')
                        ->action(function ($records, array $data) {
                            $points = (int) $data['points'];
                            $reason = $data['reason'];
                            $count = $records->count();

                            foreach ($records as $record) {
                                $record->resetSomePoints($points, $reason);
                            }

                            Notification::make()
                                ->title('Points Reset')
                                ->body("Points for {$count} users have been reset to {$points}.")
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->whereNot('user_type', 'admin');
    }

    public static function getRelations(): array
    {
        return [
            DeviceInfoRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
            'view' => Pages\ViewUser::route('/{record}/view'),
        ];
    }
}
