<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\SystemSetting;
use App\Models\UserRecommendationUsage;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Support\Facades\DB;
use Filament\Forms;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('editRecommendationLimits')
                ->label('Edit Recommendation Limits')
                ->icon('heroicon-o-adjustments-horizontal')
                ->color('primary')
                ->form(function () {
                    $record = $this->getRecord();

                    // Get the system default limit
                    $defaultLimit = SystemSetting::getValue(
                        SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                        10
                    );

                    // Get the default renewal days
                    $defaultRenewalDays = SystemSetting::getValue(
                        SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                        7
                    );

                    // Get the user's recommendation usage record
                    $usage = UserRecommendationUsage::where('user_id', $record->id)->first();

                    return [
                        Forms\Components\Section::make('Edit Limits')
                            ->description('Modify the recommendation limits for this user')
                            ->schema([
                                Forms\Components\TextInput::make('count')
                                    ->label('Usage Count')
                                    ->helperText('Set the current usage count')
                                    ->numeric()
                                    ->minValue(0)
                                    ->default(function () use ($usage) {
                                        return $usage ? $usage->count : 0;
                                    }),
                                Forms\Components\DateTimePicker::make('next_reset_at')
                                    ->label('Next Reset Date')
                                    ->helperText('When the recommendation count will reset')
                                    ->default(function () use ($usage, $defaultRenewalDays) {
                                        if ($usage && $usage->next_reset_at) {
                                            return $usage->next_reset_at;
                                        }
                                        return now()->addDays($defaultRenewalDays);
                                    }),
                                Forms\Components\Checkbox::make('reset_now')
                                    ->label('Reset Count Immediately')
                                    ->helperText('Reset the usage count to 0 and set a new reset date')
                                    ->default(false),
                            ]),
                    ];
                })
                ->action(function (array $data) {
                    $record = $this->getRecord();

                    // Get or create the user's recommendation usage record
                    $usage = UserRecommendationUsage::firstOrNew(['user_id' => $record->id]);

                    // If reset now is checked, reset the count and set new reset date
                    if ($data['reset_now']) {
                        $usage->count = 0;
                        $usage->last_reset_at = now();

                        // Get the renewal days from system settings
                        $renewalDays = SystemSetting::getValue(
                            SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                            7
                        );

                        $usage->next_reset_at = now()->addDays($renewalDays);

                        Infolists\Notifications\Notification::make()
                            ->title('Recommendation Count Reset')
                            ->body("Recommendation count for {$record->name} has been reset to 0.")
                            ->success()
                            ->send();
                    } else {
                        // Update the count and next reset date
                        $usage->count = $data['count'];
                        $usage->next_reset_at = $data['next_reset_at'];

                        Infolists\Notifications\Notification::make()
                            ->title('Recommendation Limits Updated')
                            ->body("Recommendation limits for {$record->name} have been updated.")
                            ->success()
                            ->send();
                    }

                    $usage->save();

                    // Refresh the page to show updated information
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $record]));
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Basic Information')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('name')
                                    ->label('Name'),
                                Infolists\Components\TextEntry::make('email')
                                    ->label('Email'),
                                Infolists\Components\TextEntry::make('user_type')
                                    ->label('User Type'),
                                Infolists\Components\TextEntry::make('is_anonymous')
                                    ->label('Anonymous User')
                                    ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),
                                Infolists\Components\TextEntry::make('is_demo')
                                    ->label('Demo Account')
                                    ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),
                                Infolists\Components\TextEntry::make('is_first_login')
                                    ->label('First Login')
                                    ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),
                                Infolists\Components\TextEntry::make('email_verified_at')
                                    ->label('Email Verified At')
                                    ->dateTime(),
                                Infolists\Components\TextEntry::make('created_at')
                                    ->label('Joined At')
                                    ->dateTime(),
                            ]),
                    ]),

                Infolists\Components\Section::make('Account Status')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('points')
                                    ->label('Points Balance'),
                                Infolists\Components\TextEntry::make('subscriptions.status')
                                    ->label('Subscription Status')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) {
                                            return 'No Subscription';
                                        }
                                        return $state->getLabel();
                                    })
                                    ->badge()
                                    ->color(function ($state) {
                                        if (!$state) {
                                            return 'gray';
                                        }
                                        return $state->getColor();
                                    }),
                            ]),
                    ]),

                Infolists\Components\Section::make('Recommendation Usage')
                    ->schema([
                        Infolists\Components\Grid::make(3)
                            ->schema([
                                Infolists\Components\TextEntry::make('recommendation_count')
                                    ->label('Current Usage')
                                    ->state(function ($record) {
                                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();
                                        return $usage ? $usage->count : 0;
                                    }),
                                Infolists\Components\TextEntry::make('recommendation_limit')
                                    ->label('System Limit')
                                    ->state(function () {
                                        return SystemSetting::getValue(
                                            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                                            10
                                        );
                                    }),
                                Infolists\Components\TextEntry::make('recommendation_remaining')
                                    ->label('Remaining')
                                    ->state(function ($record) {
                                        return $record->getRemainingRecommendationCount();
                                    })
                                    ->badge()
                                    ->color(function ($state) {
                                        if ($state <= 0) {
                                            return 'danger';
                                        } elseif ($state < 3) {
                                            return 'warning';
                                        } else {
                                            return 'success';
                                        }
                                    }),
                                Infolists\Components\TextEntry::make('last_reset')
                                    ->label('Last Reset')
                                    ->state(function ($record) {
                                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();
                                        return $usage && $usage->last_reset_at
                                            ? $usage->last_reset_at->format('Y-m-d H:i:s')
                                            : 'Never';
                                    }),
                                Infolists\Components\TextEntry::make('next_reset')
                                    ->label('Next Reset')
                                    ->state(function ($record) {
                                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();
                                        return $usage && $usage->next_reset_at
                                            ? $usage->next_reset_at->format('Y-m-d H:i:s')
                                            : 'Not scheduled';
                                    }),
                                Infolists\Components\TextEntry::make('days_until_reset')
                                    ->label('Days Until Reset')
                                    ->state(function ($record) {
                                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();
                                        if (!$usage || !$usage->next_reset_at) {
                                            return 'N/A';
                                        }

                                        if ($usage->next_reset_at->isPast()) {
                                            return 'Reset pending';
                                        }

                                        return $usage->next_reset_at->diffInDays(now()) . ' days';
                                    }),
                                Infolists\Components\TextEntry::make('notification_status')
                                    ->label('Notification Status')
                                    ->state(function ($record) {
                                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();
                                        if (!$usage) {
                                            return 'No usage record';
                                        }

                                        if ($record->shouldShowQuotaRenewalNotification()) {
                                            return 'Should show notification';
                                        } elseif ($usage->has_been_notified_of_renewal) {
                                            return 'Already notified';
                                        } else {
                                            return 'No notification needed';
                                        }
                                    })
                                    ->badge()
                                    ->color(function ($state) {
                                        return match ($state) {
                                            'Should show notification' => 'warning',
                                            'Already notified' => 'success',
                                            'No notification needed' => 'gray',
                                            default => 'gray',
                                        };
                                    }),
                                Infolists\Components\TextEntry::make('last_notification_shown')
                                    ->label('Last Notification Shown')
                                    ->state(function ($record) {
                                        $usage = UserRecommendationUsage::where('user_id', $record->id)->first();
                                        return $usage && $usage->last_notification_shown_at
                                            ? $usage->last_notification_shown_at->format('Y-m-d H:i:s')
                                            : 'Never';
                                    }),
                            ]),
                    ]),

                Infolists\Components\Section::make('Social Connections')
                    ->schema([
                        Infolists\Components\TextEntry::make('socialProviders.provider')
                            ->label('Sign-up Methods')
                            ->formatStateUsing(fn ($state) => ucfirst($state ?? 'Email'))
                            ->listWithLineBreaks(),
                    ]),

                Infolists\Components\Section::make('Feature Usage Statistics')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('chat_usage')
                                    ->label('Total Chat Usage')
                                    ->state(function ($record) {
                                        return $record->featureUsageStatistics()
                                            ->where('feature_type', 'chat')
                                            ->sum('usage_count');
                                    }),
                                Infolists\Components\TextEntry::make('explore_usage')
                                    ->label('Total Explore Usage')
                                    ->state(function ($record) {
                                        return $record->featureUsageStatistics()
                                            ->where('feature_type', 'explore')
                                            ->sum('usage_count');
                                    }),
                            ]),
                        Infolists\Components\TextEntry::make('daily_stats')
                            ->label('Last 7 Days Usage')
                            ->state(function ($record) {
                                $stats = $record->featureUsageStatistics()
                                    ->where('date', '>=', now()->subDays(7))
                                    ->select('feature_type', 'date', DB::raw('SUM(usage_count) as total'))
                                    ->groupBy('feature_type', 'date')
                                    ->orderBy('date', 'desc')
                                    ->get()
                                    ->groupBy('feature_type');

                                $formatted = [];
                                foreach (['chat', 'explore'] as $type) {
                                    $formatted[$type] = $stats->get($type, collect())
                                        ->pluck('total', 'date')
                                        ->toArray();
                                }

                                return view('filament.infolists.feature-usage-stats', [
                                    'chatStats' => $formatted['chat'],
                                    'exploreStats' => $formatted['explore'],
                                ])->render();
                            })
                            ->html(),
                    ]),

                Infolists\Components\Section::make('User Interests')
                    ->schema([
                        Infolists\Components\TextEntry::make('interests')
                            ->label('Interest Levels by Persona')
                            ->state(function ($record) {
                                $interests = $record->interests()
                                    ->with('agent')
                                    ->get()
                                    ->map(function ($interest) {
                                        return sprintf(
                                            '%s: %d%%',
                                            $interest->agent->name ?? 'Unknown Agent',
                                            $interest->interest_level
                                        );
                                    })
                                    ->join(', ');

                                return $interests ?: 'No interests recorded';
                            }),
                    ]),

                Infolists\Components\Section::make('Device Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('latest_device')
                            ->label('Latest Device')
                            ->state(function ($record) {
                                $latestDevice = $record->deviceInfo()->latest()->first();

                                if (!$latestDevice) {
                                    return 'No device information recorded';
                                }

                                return sprintf(
                                    '%s %s (Type: %s, OS: %s)',
                                    $latestDevice->device_brand,
                                    $latestDevice->device_model,
                                    ucfirst($latestDevice->device_type),
                                    $latestDevice->os_version
                                );
                            }),
                        Infolists\Components\TextEntry::make('device_count')
                            ->label('Total Devices')
                            ->state(function ($record) {
                                return $record->deviceInfo()->count();
                            }),
                        Infolists\Components\TextEntry::make('last_login_ip')
                            ->label('Last Login IP')
                            ->state(function ($record) {
                                $latestDevice = $record->deviceInfo()->latest()->first();
                                return $latestDevice ? $latestDevice->ip_address : 'Unknown';
                            }),
                        Infolists\Components\TextEntry::make('last_login_location')
                            ->label('Last Login Location')
                            ->state(function ($record) {
                                $latestDevice = $record->deviceInfo()->latest()->first();
                                return $latestDevice ? $latestDevice->location : 'Unknown';
                            }),
                    ])
                    ->columns(2),
            ]);
    }
}
