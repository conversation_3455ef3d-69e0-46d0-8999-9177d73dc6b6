import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/scss/style_v3.scss',
                'resources/scss/home.scss',
                'resources/js/app.js',
                'resources/js/agents.js'
            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
    resolve: {
        alias: {
            '@': '/resources',
            '~assets': path.resolve(__dirname, 'public/assets'),
        },
    },
    publicDir: 'public',
});
