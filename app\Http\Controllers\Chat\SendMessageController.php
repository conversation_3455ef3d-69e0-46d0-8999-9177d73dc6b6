<?php

namespace App\Http\Controllers\Chat;

use App\Enums\ChatMessageSender;
use App\Http\Controllers\Controller;
use App\Http\Requests\MessageRequest;
use App\Models\Agent;
use App\Models\ChatMessage;
use App\Actions\Chat\AnalyzeContextAction;
use App\Actions\Chat\ExecuteTransitionAction;
use App\Actions\Chat\GetOrCreateChatSessionAction;
use App\Actions\Chat\CreateUserMessageAction;
use App\Actions\Chat\BuildPromptContextAction;
use App\Services\AI\AIServiceFactory;
use App\Actions\Recommendations\GetRecommendationsAction;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SendMessageController extends Controller
{
    public function __construct(
        protected AnalyzeContextAction $analyzeContextAction,
        protected ExecuteTransitionAction $executeTransitionAction,
        protected GetOrCreateChatSessionAction $getOrCreateChatSessionAction,
        protected CreateUserMessageAction $createUserMessageAction,
        protected BuildPromptContextAction $buildPromptContextAction,
        protected GetRecommendationsAction $getRecommendationsAction
    ) {}

    public function __invoke(MessageRequest $request)
    {
        $user = $request->user();
        $agent = Agent::query()->findOrFail($request->agent_id);

        // Get or create chat session using the action
        $chatSession = $this->getOrCreateChatSessionAction->execute($request, $user, $agent);

        // Create user message using the action
        $userMessage = $this->createUserMessageAction->execute($chatSession, $user, $agent, $request->message_text);

        // Analyze context to check if agent transition is needed using the action directly
        $analysisResult = $this->analyzeContextAction->execute($chatSession, $request->message_text);

        // If transition is needed, log it
        if ($analysisResult['should_transition'] ?? false) {
            $targetAgent = $analysisResult['recommended_agent'];

            Log::info('Automatic agent transition initiated', [
                'chat_session_id' => $chatSession->id,
                'from_agent_id' => $chatSession->agent_id,
                'to_agent_id' => $targetAgent->id,
                'confidence_score' => $analysisResult['confidence_score'] ?? 0,
                'reason' => $analysisResult['reason'] ?? 'No reason provided'
            ]);
        }

        // Stream the response
        return $this->streamResponse($chatSession, $userMessage, $analysisResult);
    }

    /**
     * Stream AI response to the client with support for agent transitions and recommendations
     */
    protected function streamResponse($chatSession, $userMessage, array $analysisResult = []): StreamedResponse
    {
        return response()->stream(function () use ($chatSession, $userMessage, $analysisResult) {
            // Extract analysis results
            $shouldTransition = $this->shouldTransition($analysisResult);
            $shouldRecommend = $this->shouldRecommend($analysisResult);

            // Create the AI message record with empty content
            $aiMessage = $this->createInitialAiMessage($chatSession);

            // Prepare contextual data based on analysis
            $streamContext = $this->prepareStreamContext(
                $chatSession,
                $userMessage,
                $aiMessage,
                $analysisResult,
                $shouldTransition,
                $shouldRecommend
            );

            try {
                // Process the appropriate response type
                if ($shouldTransition) {
                    $this->handleTransitionStream($streamContext);
                } elseif ($shouldRecommend) {
                    $this->handleRecommendationStream($streamContext);
                } else {
                    $this->handleRegularAiStream($streamContext);
                }

                // Send completion message
                $this->sendCompletionMessage($streamContext);

            } catch (\Exception $e) {
                $this->handleStreamException($e, $streamContext);
            }

            // Update chat session last message time
            $chatSession->update(['last_message_at' => now()]);
        }, 200, $this->getStreamHeaders());
    }

    /**
     * Check if transition is needed based on analysis result
     */
    private function shouldTransition(array $analysisResult): bool
    {
        return isset($analysisResult['should_transition']) && $analysisResult['should_transition'];
    }

    /**
     * Check if recommendations are needed based on analysis result
     */
    private function shouldRecommend(array $analysisResult): bool
    {
        return isset($analysisResult['should_recommend']) && $analysisResult['should_recommend'];
    }

    /**
     * Create the initial AI message record
     */
    private function createInitialAiMessage($chatSession): ChatMessage
    {
        $aiMessage = ChatMessage::create([
            'chat_session_id' => $chatSession->id,
            'user_id' => $chatSession->user_id,
            'agent_id' => $chatSession->agent_id,
            'prompt_id' => $chatSession->agent->prompt_id,
            'message_text' => '',
            'sender' => ChatMessageSender::AGENT,
        ]);

        Log::info('AI message created', [
            'message_id' => $aiMessage->id,
        ]);

        return $aiMessage;
    }

    /**
     * Prepare the context for streaming
     */
    private function prepareStreamContext($chatSession, $userMessage, $aiMessage, $analysisResult, $shouldTransition, $shouldRecommend): array
    {
        $context = $this->buildPromptContextAction->execute($chatSession, $userMessage, $analysisResult);
        $targetAgent = $shouldTransition ? $analysisResult['recommended_agent'] : null;
        $greetingMessage = $shouldTransition ? $analysisResult['greeting_message'] : null;
        $quickReplies = $shouldTransition ? $analysisResult['quick_replies'] : null;
        $transitionResult = null;
        $transitionMetadata = null;
        $recommendationData = null;
        $fullResponseText = '';

        // If transition is needed, prepare transition data
        if ($shouldTransition && $targetAgent) {
            $transitionResult = $this->executeTransitionAction->execute(
                $chatSession,
                $chatSession->agent,
                $targetAgent,
                $greetingMessage,
                'automatic',
                $aiMessage, // Pass the existing AI message to prevent duplication
                $quickReplies
            );

            $transitionMetadata = [
                'is_transition' => true,
                'chat_session_id' => $chatSession->id,
                'agent_id' => $transitionResult['transition_message']['to_agent']['id'],
                'transition' => [
                    'from_agent_id' => $transitionResult['transition_message']['from_agent']['id'],
                    'to_agent_id' => $transitionResult['transition_message']['to_agent']['id'],
                    'prompt_starters' => $transitionResult['transition_message']['quick_replies']
                ]
            ];
        }

        // If recommendations are needed, prepare recommendation data
        if ($shouldRecommend) {
            // Get the recommended category ID if available
            $recommendedCategoryId = $analysisResult['recommended_category_id'] ?? null;

            // Log the category being used for recommendations
            if ($recommendedCategoryId) {
                Log::info('Using specific category for recommendations', [
                    'chat_session_id' => $chatSession->id,
                    'agent_id' => $chatSession->agent_id,
                    'category_id' => $recommendedCategoryId
                ]);
            }

            $recommendationData = $this->getRecommendationsAction->execute(
                $chatSession->agent,
                $chatSession->user,
                $aiMessage,
                $analysisResult['recommendation_count'] ?? null,
                $recommendedCategoryId
            );
        }

        return [
            'chatSession' => $chatSession,
            'userMessage' => $userMessage,
            'aiMessage' => $aiMessage,
            'promptContext' => $context,
            'shouldTransition' => $shouldTransition,
            'shouldRecommend' => $shouldRecommend,
            'targetAgent' => $targetAgent,
            'transitionResult' => $transitionResult,
            'transitionMetadata' => $transitionMetadata,
            'recommendationData' => $recommendationData,
            'fullResponseText' => $fullResponseText,
        ];
    }

    /**
     * Handle streaming for agent transitions
     */
    private function handleTransitionStream(array $context): void
    {
        $transitionText = $context['transitionResult']['transition_message']['message'];
        $transitionMessageId = $context['transitionResult']['transition_message']['id'];
        $transitionChunkSize = 20; // characters per chunk
        $position = 0;

        // Stream transition chunks
        while ($position < strlen($transitionText)) {
            $currentChunk = substr($transitionText, $position, $transitionChunkSize);
            $position += $transitionChunkSize;

            // Prepare transition metadata (without completed flag)
            $transMetadata = [
                'chat_session_id' => $context['chatSession']->id,
                'agent_id' => $context['transitionResult']['transition_message']['to_agent']['id'],
                'is_transition' => true,
                'transition' => [
                    'from_agent_id' => $context['transitionResult']['transition_message']['from_agent']['id'],
                    'to_agent_id' => $context['transitionResult']['transition_message']['to_agent']['id'],
                    'prompt_starters' => $context['transitionResult']['transition_message']['quick_replies']
                ]
            ];

            $this->sendStreamChunk(
                $transitionMessageId,
                $currentChunk,
                'text', // TODO should be transition message type
                'agent',
                $transMetadata
            );
        }

        // Store the transition message
        $context['aiMessage']->update(['message_text' => $transitionText]);

        // Track OpenAI API usage for transition messages
        try {
            $aiService = AIServiceFactory::create();

            // Add tracking metadata for OpenAI API usage
            $trackingMetadata = [
                'source' => 'SendMessageController',
                'action' => 'agent_transition',
                'chat_session_id' => $context['chatSession']->id,
                'user_message_id' => $context['userMessage']->id,
                'is_transition' => true,
                'transition_from_agent_id' => $context['transitionResult']['transition_message']['from_agent']['id'],
                'transition_to_agent_id' => $context['transitionResult']['transition_message']['to_agent']['id'],
            ];

            // Estimate token counts (rough approximation: ~4 chars per token)
            $promptTokens = 0; // No prompt tokens for transition messages
            $completionTokens = ceil(strlen($transitionText) / 4);

            // Create a response array with estimated token usage
            $responseArray = [
                'usage' => [
                    'prompt_tokens' => $promptTokens,
                    'completion_tokens' => $completionTokens,
                    'total_tokens' => $promptTokens + $completionTokens,
                ]
            ];

            // Get the model from the agent's prompt or use default
            $model = $context['chatSession']->agent->prompt->model_type ?? 'gpt-4';

            // Track the usage
            $trackOpenAIUsageAction = app(\App\Actions\OpenAI\TrackOpenAIUsageAction::class);
            $trackOpenAIUsageAction->execute(
                $model,
                $responseArray,
                $trackingMetadata,
                $context['chatSession']->user_id,
                $context['aiMessage']->id,
                'SendMessageController',
                'agent_transition',
                $context['chatSession']->id
            );
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to track OpenAI API usage for transition', [
                'error' => $e->getMessage(),
                'chat_session_id' => $context['chatSession']->id,
            ]);
        }
    }

    /**
     * Handle streaming for recommendations
     */
    private function handleRecommendationStream(array $context): void
    {
        if (empty($context['recommendationData']) || empty($context['recommendationData']['formatted_text'])) {
            return;
        }

        // Check if this is a limited recommendation response
        $isLimited = isset($context['recommendationData']['limited']) && $context['recommendationData']['limited'];
        $messageType = $isLimited ? 'limited_recommendation' : 'recommendation';

        $recommendationText = $context['recommendationData']['formatted_text'];
        $recommendationId = $context['recommendationData']['recommendation_id'];
        $recommendationChunkSize = 20; // characters per chunk
        $position = 0;

        // Stream recommendation chunks
        while ($position < strlen($recommendationText)) {
            $currentChunk = substr($recommendationText, $position, $recommendationChunkSize);
            $position += $recommendationChunkSize;

            // Prepare recommendation metadata (without completed flag)
            $recMetadata = [
                'chat_session_id' => $context['chatSession']->id,
                'agent_id' => $context['chatSession']->agent_id,
                'recommendation_id' => $recommendationId
            ];

            $this->sendStreamChunk(
                $context['aiMessage']->id,
                $currentChunk,
                $messageType,
                'agent',
                $recMetadata
            );
        }

        // Update the message with recommendation text
        $context['aiMessage']->update(['message_text' => $recommendationText, 'is_recommended' => true]);

        // Track OpenAI API usage for recommendation messages
        try {
            // Add tracking metadata for OpenAI API usage
            $trackingMetadata = [
                'source' => 'SendMessageController',
                'action' => 'get_recommendations',
                'chat_session_id' => $context['chatSession']->id,
                'user_message_id' => $context['userMessage']->id,
                'is_recommendation' => true,
                'recommendation_id' => $recommendationId,
            ];

            // Add category information to metadata if available
            if (isset($context['recommendationData']['category_id'])) {
                $trackingMetadata['category_id'] = $context['recommendationData']['category_id'];
            }

            // Add place information to metadata if available
            if (!empty($context['recommendationData']['places'])) {
                $placeIds = array_map(function($place) {
                    return $place['id'];
                }, $context['recommendationData']['places']);

                $trackingMetadata['place_ids'] = $placeIds;
                $trackingMetadata['place_count'] = count($placeIds);
            }

            // Estimate token counts (rough approximation: ~4 chars per token)
            // For recommendations, we should also consider the prompt context that was used
            $promptTokens = ceil(strlen($context['promptContext'] ?? '') / 4);
            $completionTokens = ceil(strlen($recommendationText) / 4);

            // Create a response array with estimated token usage
            $responseArray = [
                'usage' => [
                    'prompt_tokens' => $promptTokens,
                    'completion_tokens' => $completionTokens,
                    'total_tokens' => $promptTokens + $completionTokens,
                ]
            ];

            // Get the model from the agent's prompt or use default
            $model = $context['chatSession']->agent->prompt->model_type ?? 'gpt-4';

            // Track the usage
            $trackOpenAIUsageAction = app(\App\Actions\OpenAI\TrackOpenAIUsageAction::class);
            $trackOpenAIUsageAction->execute(
                $model,
                $responseArray,
                $trackingMetadata,
                $context['chatSession']->user_id,
                $context['aiMessage']->id,
                'SendMessageController',
                'get_recommendations',
                $context['chatSession']->id
            );
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to track OpenAI API usage for recommendation', [
                'error' => $e->getMessage(),
                'chat_session_id' => $context['chatSession']->id,
                'recommendation_id' => $recommendationId,
            ]);
        }
    }

    /**
     * Handle streaming for regular AI responses
     */
    private function handleRegularAiStream(array &$context): void
    {
        $aiService = AIServiceFactory::create();
        $fullResponseText = '';

        // Add tracking metadata for OpenAI API usage
        $trackingMetadata = [
            'source' => 'SendMessageController',
            'action' => 'send_message',
            'chat_session_id' => $context['chatSession']->id,
            'user_message_id' => $context['userMessage']->id,
        ];

        $aiService->streamResponse(
            $context['promptContext'],
            function ($chunk, $fullResponse) use (&$context, &$fullResponseText) {
                // Update our tracking variable
                $fullResponseText = $fullResponse;

                // Update the message in the database periodically
                if (strlen($fullResponse) % 100 === 0 || strlen($chunk) === 0) {
                    $context['aiMessage']->update(['message_text' => $fullResponse]);
                }

                // Prepare base metadata
                $metadata = [
                    'chat_session_id' => $context['chatSession']->id,
                    'agent_id' => $context['chatSession']->agent_id
                ];

                // Include transition metadata if available
                if ($context['shouldTransition'] && $context['transitionMetadata']) {
                    $metadata = array_merge($metadata, $context['transitionMetadata']);
                }

                $this->sendStreamChunk(
                    $context['aiMessage']->id,
                    $chunk,
                    'text',
                    'agent',
                    $metadata
                );
            },
            [
                'model' => $context['chatSession']->agent->prompt->model_type ?? 'gpt-4',
                'user_id' => $context['chatSession']->user_id,
                'chat_message_id' => $context['aiMessage']->id,
                'chat_session_id' => $context['chatSession']->id,
                'source' => 'SendMessageController',
                'action' => 'send_message',
                'metadata' => $trackingMetadata
            ]
        );

        // Store the full response text in the context for the completion message
        $context['fullResponseText'] = $fullResponseText;

        // Final update of the message
        $context['aiMessage']->update(['message_text' => $fullResponseText]);
    }

    /**
     * Send completion message based on response type
     */
    private function sendCompletionMessage(array $context): void
    {
        // For transitions
        if ($context['shouldTransition'] && isset($context['transitionResult'])) {
            $baseMetadata = [
                'chat_session_id' => $context['chatSession']->id,
                'agent_id' => $context['transitionResult']['transition_message']['to_agent']['id'],
                'is_transition' => true,
                'transition' => [
                    'from_agent_id' => $context['transitionResult']['transition_message']['from_agent']['id'],
                    'to_agent_id' => $context['transitionResult']['transition_message']['to_agent']['id'],
                    'prompt_starters' => $context['transitionResult']['transition_message']['quick_replies']
                ]
            ];

            $this->sendStreamChunk(
                $context['transitionResult']['transition_message']['id'],
                $context['transitionResult']['transition_message']['message'],
                'text', // TODO should be transition message type
                'agent',
                array_merge($baseMetadata, ['completed' => true])
            );
        }
        // For recommendations or limited recommendations
        elseif ($context['shouldRecommend'] && $context['recommendationData']) {
            // Check if this is a limited recommendation response
            $isLimited = isset($context['recommendationData']['limited']) && $context['recommendationData']['limited'];
            $messageType = $isLimited ? 'limited_recommendation' : 'recommendation';

            // Prepare metadata
            $metadata = [
                'completed' => true,
                'chat_session_id' => $context['chatSession']->id,
                'agent_id' => $context['chatSession']->agent_id,
            ];

            // Add recommendation ID if available
            if (isset($context['recommendationData']['recommendation_id'])) {
                $metadata['recommendation_id'] = $context['recommendationData']['recommendation_id'];
            }

            // Add limit info if available
            if (isset($context['recommendationData']['limit_info'])) {
                $metadata['limit_info'] = $context['recommendationData']['limit_info'];
            }

            // Add place images as a simple array of URLs to metadata if available
            if (!empty($context['recommendationData']['places'])) {
                $metadata['images_places'] = array_map(function($place) {
                    return $place['image_url'];
                }, $context['recommendationData']['places']);
            }

            $this->sendStreamChunk(
                $context['aiMessage']->id,
                $context['recommendationData']['formatted_text'],
                $messageType,
                'agent',
                $metadata
            );
        }
        // For regular messages
        else {
            $this->sendStreamChunk(
                $context['aiMessage']->id,
                $context['fullResponseText'],
                'text',
                'agent',
                [
                    'completed' => true,
                    'chat_session_id' => $context['chatSession']->id,
                    'agent_id' => $context['chatSession']->agent_id,
                ]
            );
        }
    }

    /**
     * Send a chunk of data to the stream
     */
    private function sendStreamChunk($id, $content, $type, $sender, $metadata): void
    {
        echo "data: " . json_encode([
                'id' => $id,
                'content' => $content,
                'type' => $type,
                'sender' => $sender,
                'timestamp' => now()->toIso8601String(),
                'metadata' => $metadata
            ], JSON_UNESCAPED_SLASHES) . "\n\n";
        ob_flush();
        flush();
    }

    /**
     * Handle exceptions during streaming
     */
    private function handleStreamException(\Exception $e, array $context): void
    {
        // Prepare error metadata
        $errorMetadata = [
            'error' => true,
            'chat_session_id' => $context['chatSession']->id,
            'agent_id' => $context['chatSession']->agent_id,
        ];

        // Include transition metadata if applicable
        if ($context['shouldTransition'] && $context['transitionMetadata']) {
            $errorMetadata = array_merge($errorMetadata, $context['transitionMetadata']);
        }

        // Include recommendation_id and place images in error metadata if applicable
        if ($context['shouldRecommend'] && $context['recommendationData'] && isset($context['recommendationData']['recommendation_id'])) {
            $errorMetadata['recommendation_id'] = $context['recommendationData']['recommendation_id'];
            $errorMetadata['completed'] = true;

            // Add place images as a simple array of URLs to error metadata if available
            if (!empty($context['recommendationData']['places'])) {
                $errorMetadata['images_places'] = array_map(function($place) {
                    return $place['image_url'];
                }, $context['recommendationData']['places']);
            }
        }

        // Determine the appropriate type for the error message
        $errorType = 'text';
        if ($context['shouldRecommend']) {
            $errorType = 'recommendation';
        } else if ($context['shouldTransition']) {
            $errorType = 'transition';
        }

        $this->sendStreamChunk(
            $context['aiMessage']->id,
            'An error occurred while generating the response.',
            $errorType,
            'agent',
            $errorMetadata
        );

        // Log the error
        Log::error('AI Response Generation Error', [
            'error' => $e->getMessage(),
            'stack_trace' => $e->getTraceAsString(),
            'chat_session_id' => $context['chatSession']->id,
            'user_message_id' => $context['userMessage']->id,
        ]);
    }

    /**
     * Get the headers for the stream response
     */
    private function getStreamHeaders(): array
    {
        return [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
        ];
    }
}
