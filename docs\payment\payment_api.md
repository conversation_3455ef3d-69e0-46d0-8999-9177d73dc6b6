# PaymentApi

The `PaymentApi` class is responsible for making API calls to the backend for payment-related operations.

## Location

`lib/features/payment/data/api.dart`

## Dependencies

- `bond_network`: For making HTTP requests
- `PaymentInitialization`: Data model for initialization response
- `WalletChargeDto`: Data model for payment requests

## Class Structure

```dart
class PaymentApi {
  final BondFire _bondFire;

  PaymentApi(this._bondFire);

  Future<PaymentInitialization> initializePayment(WalletChargeDto dto);
  Future<UserApiResult> processPayment(WalletChargeDto dto);
}
```

## Key Methods

### initializePayment

Calls the `/payment/initialize` endpoint to create a transaction record and get a transaction ID:

```dart
/// Initialize payment with the backend
/// Returns transaction ID and payment data
Future<PaymentInitialization> initializePayment(WalletChargeDto dto) => _bondFire
    .post<PaymentInitialization>('/payment/initialize')
    .body({
      'amount_price': dto.amountPrice,
      'amount_point': dto.amountPoint,
    })
    .header(Api.headers())
    .factory(PaymentInitialization.fromJson)
    .errorFactory(ServerError.fromJson)
    .execute();
```

### processPayment

Calls the `/payment/process` endpoint to update the user's wallet after a successful payment:

```dart
/// Process payment after successful payment gateway transaction
/// Updates user wallet with purchased points
Future<UserApiResult> processPayment(WalletChargeDto dto) => _bondFire
    .post<UserApiResult>('/payment/process')
    .body(dto.toJson())
    .header(Api.headers())
    .factory(UserApiResult.fromJson)
    .errorFactory(ServerError.fromJson)
    .cacheCustomKey('user', path: 'data')
    .execute();
```

## API Endpoints

### /payment/initialize

**Request:**
```json
{
  "amount_price": 100,
  "amount_point": 1000
}
```

**Response:**
```json
{
  "success": true,
  "transaction_id": "#TID-12345",
  "payment_data": {
    "tran_ref": "TST123456789"
  }
}
```

### /payment/process

**Request:**
```json
{
  "amount_price": 100,
  "amount_point": 1000,
  "payment_reference": "TST123456789",
  "payment_method": "card",
  "vat_amount": 15,
  "total_amount": 115
}
```

**Response:**
```json
{
  "data": {
    "id": 123,
    "name": "User Name",
    "available_point": 2000,
    "total_point": 5000
  },
  "meta": {
    "success": true,
    "message": "Points added successfully"
  }
}
```

## Usage Example

```dart
// In PaymentService
Future<Map<String, dynamic>> startCardPayment({
  required double amount,
  required String currency,
  required String cartDescription,
  required WalletChargeDto chargeDto,
}) async {
  // Initialize payment with backend
  final initialization = await paymentApi.initializePayment(chargeDto);
  
  // Use the transaction ID for PayTabs SDK
  // ...
}

// In PaymentProvider
Future<void> _handlePaymentResult(
  BuildContext context,
  Map<String, dynamic> result,
  WalletChargeDto chargeDto,
) async {
  if (result["status"] == "success") {
    // Update chargeDto with payment reference
    final updatedChargeDto = chargeDto.copyWith(
      paymentReference: result["data"]["transactionReference"],
    );

    // Call API to process payment
    final chargeUser = await paymentApi.processPayment(updatedChargeDto);
    
    // Handle response
    // ...
  }
}
```
