<?php

use App\Http\Controllers\Api\CategoryPlacesController;
use App\Http\Controllers\Api\ChatHistoryController;
use App\Http\Controllers\Api\NearbyPlacesController;
use App\Http\Controllers\Auth\SocialLoginController;
use App\Http\Controllers\Chat\SendMessageController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\FeatureUsageController;
use App\Http\Controllers\Api\AdvertisementController;
use App\Http\Controllers\Api\AgentController;
use App\Http\Controllers\Api\HomeController;
use App\Http\Controllers\Api\PlaceController;
use App\Http\Controllers\Api\RecommendationPlacesController;
use App\Http\Controllers\Api\FavoritePlaceController;
use App\Http\Controllers\Auth\AnonymousUserController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Dashboard\ComingSoonController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\UserAgentPreferenceController;
use App\Http\Controllers\Api\UserProfileController;
use App\Http\Controllers\Auth\UpdateUserTokenController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::middleware(['auth:sanctum', 'admin'])->prefix('admin')->group(function () {
    Route::get('/feature-usage', [FeatureUsageController::class, 'getStatistics']);
    Route::post('/feature-usage/reset', [FeatureUsageController::class, 'resetStatistics']);
});

Route::prefix('places')->group(function () {
    Route::get('/', [PlaceController::class, 'index']);
    Route::get('/{place}', [PlaceController::class, 'show']);

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/', [PlaceController::class, 'store']);
        Route::put('/{place}', [PlaceController::class, 'update']);
        Route::delete('/{place}', [PlaceController::class, 'destroy']);

        // Recommendation routes
        Route::get('/recommendation/{recommendation:id}', RecommendationPlacesController::class);

        // Favorite routes
        Route::prefix('favorite')->group(function () {
            Route::post('/', [FavoritePlaceController::class, 'toggleFavorite']);
            Route::get('/list', [FavoritePlaceController::class, 'getList']);
        });
    });
});

// Anonymous user route
Route::prefix('users')->group(function () {
    Route::post('anonymous-login', AnonymousUserController::class);

    // Social login route
    Route::post('social-login', SocialLoginController::class);
});

// Agent routes
Route::get('/agents', [AgentController::class, 'index']);

// Home routes
Route::prefix('home')->group(function () {
    // grouped prefix for places
    Route::middleware('auth:sanctum')->prefix('places')->group(function () {
        Route::get('choices', [HomeController::class, 'choices']);
        Route::get('trends', [HomeController::class, 'trends']);
    });

    // Advertisements route
    Route::get('ads', AdvertisementController::class);
});


Route::middleware('auth:sanctum')->group(function () {
    Route::post('/users/register', RegisterController::class);

    // Chat routes
    Route::post('/chat/message', SendMessageController::class);

    // Chat History routes
    Route::prefix('chat/history')->group(function () {
        Route::get('/', [ChatHistoryController::class, 'index']);
        Route::get('/{chatSession:id}', [ChatHistoryController::class, 'show']);
        Route::delete('/{chatSession:id}', [ChatHistoryController::class, 'destroy']);
    });

    // Category Places Route
    Route::get('/categories/{category:id}/places', CategoryPlacesController::class);

    // Nearby Places Route
    Route::get('map/places', NearbyPlacesController::class);

    // User Agent Preferences routes
    Route::get('/users/interests', [UserAgentPreferenceController::class, 'index']);
    Route::post('/users/interests', [UserAgentPreferenceController::class, 'store']);

    // User Profile route
    Route::get('/user/profile', UserProfileController::class);

    // Update user token route
    Route::post('/users/update-user-token', UpdateUserTokenController::class);

    // Quota renewal notification route
    Route::post('/quota-renewal/mark-as-seen', [App\Http\Controllers\Api\QuotaRenewalNotificationController::class, 'markAsSeen']);


    // Subscription routes
    Route::prefix('subscriptions')->group(function () {
        Route::get('/plans', [SubscriptionController::class, 'getPlans']);
        Route::get('/status', [SubscriptionController::class, 'getStatus']);
        Route::post('/purchase/process', [SubscriptionController::class, 'processPurchase']);
        Route::post('/cancel', [SubscriptionController::class, 'cancelSubscription']);
        Route::post('/reactivate', [SubscriptionController::class, 'reactivateSubscription']);
        Route::get('/eligibility', [SubscriptionController::class, 'checkEligibility']);
        Route::get('/reactivation/eligibility', [SubscriptionController::class, 'checkReactivationEligibility']);
        Route::get('/last', [SubscriptionController::class, 'getLastSubscription']);
        Route::get('/history', [SubscriptionController::class, 'getSubscriptionHistory']);
    });

    // Logout
    Route::post('/logout', App\Http\Controllers\Auth\LogoutController::class);

    // Delete account
    Route::delete('/account', App\Http\Controllers\Auth\DeleteAccountController::class);
});

Route::post('/messages', [ComingSoonController::class, 'store']);

// Settings API
Route::prefix('settings')->group(function () {
    Route::get('/', [SettingsController::class, 'index']);
    Route::get('/{key}', [SettingsController::class, 'show']);
});
