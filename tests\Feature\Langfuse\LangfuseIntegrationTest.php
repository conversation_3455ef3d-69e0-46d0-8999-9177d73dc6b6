<?php

namespace Tests\Feature\Langfuse;

use App\Actions\Langfuse\TrackChatSessionAction;
use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\User;
use App\Services\Langfuse\LangfuseTrackingService;
use App\Enums\ChatMessageSender;
use App\Enums\UserType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LangfuseIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Agent $agent;
    protected ChatSession $chatSession;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test configuration
        config([
            'langfuse.enabled' => true,
            'langfuse.public_key' => 'test_public_key',
            'langfuse.secret_key' => 'test_secret_key',
            'langfuse.host' => 'https://test.langfuse.com',
            'langfuse.error_handling.fail_silently' => true,
            'langfuse.tracing.trace_in_testing' => true,
        ]);

        // Create test data
        $this->user = User::factory()->create([
            'type' => UserType::CUSTOMER,
        ]);

        $this->agent = Agent::factory()->create([
            'name' => 'Test Agent',
        ]);

        $this->chatSession = ChatSession::factory()->create([
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
        ]);
    }

    public function test_chat_session_tracking_action_works()
    {
        $action = app(TrackChatSessionAction::class);
        
        // This should not throw an exception even if Langfuse is not actually available
        $sessionId = $action->execute($this->chatSession);
        
        // Since we're using a test environment, the actual API call will fail
        // but it should fail gracefully and return null
        $this->assertNull($sessionId);
    }

    public function test_langfuse_tracking_service_handles_chat_messages()
    {
        $trackingService = app(LangfuseTrackingService::class);

        $userMessage = ChatMessage::factory()->create([
            'chat_session_id' => $this->chatSession->id,
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'sender' => ChatMessageSender::USER,
            'message_text' => 'Hello, I need help with something.',
        ]);

        $agentMessage = ChatMessage::factory()->create([
            'chat_session_id' => $this->chatSession->id,
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'sender' => ChatMessageSender::AGENT,
            'message_text' => 'Hello! I\'d be happy to help you.',
        ]);

        // Test tracking user message
        $traceId = $trackingService->trackChatMessage($userMessage);
        $this->assertNull($traceId); // Should fail gracefully in test environment

        // Test tracking agent message with OpenAI response
        $openaiResponse = [
            'id' => 'chatcmpl-test123',
            'model' => 'gpt-4',
            'choices' => [
                [
                    'message' => [
                        'content' => 'Hello! I\'d be happy to help you.',
                    ],
                ],
            ],
            'usage' => [
                'prompt_tokens' => 15,
                'completion_tokens' => 10,
                'total_tokens' => 25,
            ],
        ];

        $traceId = $trackingService->trackChatMessage(
            $agentMessage,
            'You are a helpful assistant. User: Hello, I need help with something.',
            $openaiResponse
        );
        
        $this->assertNull($traceId); // Should fail gracefully in test environment
    }

    public function test_langfuse_service_is_properly_registered()
    {
        $this->assertInstanceOf(
            LangfuseTrackingService::class,
            app(LangfuseTrackingService::class)
        );
    }

    public function test_langfuse_disabled_in_testing_by_default()
    {
        config(['langfuse.tracing.trace_in_testing' => false]);
        
        $trackingService = app(LangfuseTrackingService::class);
        
        $message = ChatMessage::factory()->create([
            'chat_session_id' => $this->chatSession->id,
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'sender' => ChatMessageSender::USER,
            'message_text' => 'Test message',
        ]);

        $traceId = $trackingService->trackChatMessage($message);
        
        // Should return null when disabled in testing
        $this->assertNull($traceId);
    }

    public function test_user_feedback_tracking()
    {
        $trackingService = app(LangfuseTrackingService::class);

        $result = $trackingService->trackUserFeedback(
            'trace_123',
            'user_rating',
            5,
            'Great response!',
            'generation_123'
        );

        // Should fail gracefully and return false in test environment
        $this->assertFalse($result);
    }

    public function test_session_id_generation_is_consistent()
    {
        $trackingService = app(LangfuseTrackingService::class);
        
        $reflection = new \ReflectionClass($trackingService);
        $method = $reflection->getMethod('generateSessionId');
        $method->setAccessible(true);
        
        $sessionId1 = $method->invoke($trackingService, $this->chatSession);
        $sessionId2 = $method->invoke($trackingService, $this->chatSession);
        
        $this->assertEquals($sessionId1, $sessionId2);
        $this->assertEquals('chat_session_' . $this->chatSession->id, $sessionId1);
    }

    public function test_langfuse_configuration_is_loaded()
    {
        $this->assertTrue(config('langfuse.enabled'));
        $this->assertEquals('test_public_key', config('langfuse.public_key'));
        $this->assertEquals('test_secret_key', config('langfuse.secret_key'));
        $this->assertEquals('https://test.langfuse.com', config('langfuse.host'));
    }
}
