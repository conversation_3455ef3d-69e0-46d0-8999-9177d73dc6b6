<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use LevelUp\Experience\Concerns\GiveExperience;
use LevelUp\Experience\Concerns\HasAchievements;
use LevelUp\Experience\Concerns\HasStreaks;
use Illuminate\Support\Str;
use App\Models\PointAdjustment;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements FilamentUser, HasMedia
{
    use HasApiTokens, HasFactory, Notifiable, HasAchievements, HasStreaks, GiveExperience, InteractsWithMedia, SoftDeletes;

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_points_renewal_at' => 'datetime',
            'next_points_renewal_at' => 'datetime',
            'password' => 'hashed',
            'is_anonymous' => 'boolean',
            'is_first_login' => 'boolean',
            'is_demo' => 'boolean',
            'is_premium' => 'boolean',
            'points' => 'integer',
            'deleted_at' => 'datetime',
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (!$user->referral_code) {
                $user->referral_code = Str::random(10);
            }
        });

        static::created(function ($user) {
            if ($user->is_anonymous) {
                $defaultPoints = SystemSetting::getValue(SystemSetting::KEY_DEFAULT_GUEST_POINTS, 100);
                $renewalDays = SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_DAYS, 7);

                $user->last_points_renewal_at = now();
                $user->next_points_renewal_at = now()->addDays($renewalDays);
                $user->save();

                $user->addSomePoints($defaultPoints, 'Initial points for guest user');
            } else {
                $defaultPoints = SystemSetting::getValue(SystemSetting::KEY_DEFAULT_REGISTERED_POINTS, 500);
                $renewalHours = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_HOURS, 24);

                $user->last_points_renewal_at = now();
                $user->next_points_renewal_at = now()->addHours($renewalHours);
                $user->save();

                $user->addSomePoints($defaultPoints, 'Initial points for registered user');
            }
        });

        static::updating(function ($user) {
            if ($user->isDirty('is_anonymous') && !$user->is_anonymous) {
                // Convert from anonymous to registered user
                $defaultPoints = SystemSetting::getValue(SystemSetting::KEY_DEFAULT_REGISTERED_POINTS, 500);
                $renewalHours = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_HOURS, 24);

                $user->addSomePoints($defaultPoints, 'Initial points for registered user');
                $user->last_points_renewal_at = now();
                $user->next_points_renewal_at = now()->addHours($renewalHours);
            }
        });
    }

    public function socialProviders()
    {
        return $this->hasMany(SocialProvider::class);
    }

    public function agentPreferences()
    {
        return $this->hasMany(UserAgentPreference::class);
    }

    public function chatSessions()
    {
        return $this->hasMany(ChatSession::class);
    }

    public function messageReactions()
    {
        return $this->hasMany(MessageReaction::class);
    }

    public function messageReports()
    {
        return $this->hasMany(MessageReport::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function hasActiveSubscription(): bool
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->exists();
    }

    public function getActiveSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->latest()
            ->first();
    }

    /**
     * Alias for getActiveSubscription for better readability
     */
    public function activeSubscription()
    {
        return $this->getActiveSubscription();
    }

    public function paymentHistory()
    {
        return $this->hasMany(PaymentHistory::class);
    }

    public function recommendations()
    {
        return $this->hasMany(Recommendation::class);
    }

    public function featureUsageStatistics()
    {
        return $this->hasMany(FeatureUsageStatistic::class);
    }

    public function deviceInfo()
    {
        return $this->hasMany(DeviceInfo::class);
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    /**
     * Register media collections for the model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile();
    }

    /**
     * Get the user's avatar URL.
     */
    public function avatar(): Attribute
    {
        return Attribute::get(function () {
            return $this->getFirstMediaUrl('avatar') ?: null;
        });
    }

    public function addSomePoints(int $amount, string $reason, ?int $adminId = null, string $type = 'add'): void
    {
        $this->addPoints($amount, reason: $reason);

        // Create point adjustment record without triggering observers
        PointAdjustment::withoutEvents(function () use ($amount, $type, $reason, $adminId) {
            $admin = $adminId ? User::find($adminId) : User::where('user_type', 'admin')->first();
            
            $this->pointAdjustments()->create([
                'amount' => $amount,
                'type' => $type,
                'reason' => $reason,
                'admin_id' => $admin?->id,
            ]);
        });
    }

    public function deductSomePoints(int $amount, string $reason, ?int $adminId = null): void
    {
        $this->deductPoints($amount, reason: $reason);

        // Create point adjustment record without triggering observers
        PointAdjustment::withoutEvents(function () use ($amount, $reason, $adminId) {
            $admin = $adminId ? User::find($adminId) : User::where('user_type', 'admin')->first();
            
            $this->pointAdjustments()->create([
                'amount' => $amount,
                'type' => 'deduct',
                'reason' => $reason,
                'admin_id' => $admin?->id,
            ]);
        });
    }

    public function resetSomePoints(string $amount, ?string $reason = null): void
    {
        // Create point adjustment record without triggering observers
        PointAdjustment::withoutEvents(function () use ($amount, $reason) {
            $admin = User::where('user_type', 'admin')->first();
            
            $this->pointAdjustments()->create([
                'amount' => $amount,
                'type' => 'reset',
                'reason' => $reason ?? 'Points reset',
                'admin_id' => $admin?->id,
            ]);
        });

        $this->setPoints($amount);
    }

    public function pointAdjustments()
    {
        return $this->hasMany(PointAdjustment::class);
    }

    public function interests()
    {
        return $this->hasMany(UserInterest::class);
    }

    public function referrer()
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    public function referrals()
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    public function referralLogs()
    {
        return $this->hasMany(ReferralLog::class, 'referrer_id');
    }

    public function referredByLogs()
    {
        return $this->hasMany(ReferralLog::class, 'referred_user_id');
    }

    public function processReferral(User $referredUser)
    {
        if (!SystemSetting::getValue('referral_system_active', true)) {
            return false;
        }

        $points = SystemSetting::getValue('referral_points_per_referral', 100);

        $log = ReferralLog::create([
            'referrer_id' => $this->id,
            'referred_user_id' => $referredUser->id,
            'points_awarded' => $points,
            'status' => 'completed'
        ]);

        $this->addSomePoints($points, "Referral bonus for referring {$referredUser->name}");

        return $log;
    }

    public function shouldRenewPoints(): bool
    {
        if (is_null($this->next_points_renewal_at)) {
            return true;
        }

        return $this->next_points_renewal_at->isPast();
    }

    public function renewPoints(): void
    {
        if ($this->is_anonymous) {
            $renewalAmount = SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_AMOUNT, 100);
            $renewalDays = SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_DAYS, 7);

            $this->addSomePoints($renewalAmount, 'Periodic points renewal for guest user');
            $this->last_points_renewal_at = now();
            $this->next_points_renewal_at = now()->addDays($renewalDays);
        } else {
            $renewalAmount = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_AMOUNT, 200);
            $renewalHours = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_HOURS, 24);

            $this->addSomePoints($renewalAmount, 'Periodic points renewal for registered user');
            $this->last_points_renewal_at = now();
            $this->next_points_renewal_at = now()->addHours($renewalHours);
        }

        $this->save();
    }

    public function getPointsAttribute()
    {
        return $this->getPoints();
    }

    /**
     * Get the place favorites for the user.
     */
    public function placeFavorites()
    {
        return $this->hasMany(PlaceFavorite::class);
    }

    /**
     * Get the recommendation usage for the user.
     */
    public function recommendationUsage()
    {
        return $this->hasOne(UserRecommendationUsage::class);
    }

    /**
     * Check if the user has reached their recommendation limit
     */
    public function hasReachedRecommendationLimit(): bool
    {
        return UserRecommendationUsage::hasReachedLimit($this);
    }

    /**
     * Get the remaining recommendation count for the user
     */
    public function getRemainingRecommendationCount(): int
    {
        return UserRecommendationUsage::getRemainingCount($this);
    }

    /**
     * Increment the user's recommendation usage count
     */
    public function incrementRecommendationUsage(): UserRecommendationUsage
    {
        return UserRecommendationUsage::incrementUsage($this);
    }

    /**
     * Check if the user should be shown a quota renewal notification
     */
    public function shouldShowQuotaRenewalNotification(): bool
    {
        return UserRecommendationUsage::shouldShowRenewalNotification($this);
    }

    /**
     * Mark that the user has been notified of quota renewal
     */
    public function markQuotaRenewalAsNotified(): void
    {
        UserRecommendationUsage::markAsNotified($this);
    }
}
