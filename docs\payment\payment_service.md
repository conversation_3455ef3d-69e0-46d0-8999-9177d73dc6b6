# PaymentService

The `PaymentService` class is responsible for handling the interaction with the PayTabs SDK and initializing payments with the backend.

## Location

`lib/features/payment/services/payment_service.dart`

## Dependencies

- `flutter_paytabs_bridge`: For interacting with the PayTabs SDK
- `PaymentApi`: For making API calls to the backend

## Class Structure

```dart
class PaymentService {
  // Configuration details
  final String profileId;
  final String serverKey;
  final String clientKey;
  final String merchantName;
  final String merchantCountryCode;
  final String merchantApplePayIdentifier;
  final PaymentApi paymentApi;

  PaymentService({
    required this.profileId,
    required this.serverKey,
    required this.clientKey,
    required this.merchantName,
    required this.merchantCountryCode,
    required this.merchantApplePayIdentifier,
    required this.paymentApi,
  });

  // Methods
  Future<Map<String, dynamic>> startCardPayment({...});
  Future<Map<String, dynamic>> startApplePayPayment({...});
  PaymentSdkConfigurationDetails _createBaseConfiguration({...});
  BillingDetails? _createBillingDetails();
}
```

## Key Methods

### startCardPayment

Initializes and processes a card payment:

```dart
Future<Map<String, dynamic>> startCardPayment({
  required double amount,
  required String currency,
  required String cartDescription,
  required WalletChargeDto chargeDto,
}) async {
  // Initialize payment with backend
  final initialization = await paymentApi.initializePayment(chargeDto);

  // Create billing details
  final billingDetails = _createBillingDetails();

  // Create configuration
  final configuration = _createBaseConfiguration(
    amount: amount,
    currency: currency,
    cartDescription: cartDescription,
    cartId: initialization.transactionId,
    billingDetails: billingDetails,
  );

  // Create completer to handle async result
  final completer = Completer<Map<String, dynamic>>();

  // Start payment
  FlutterPaytabsBridge.startCardPayment(configuration, (event) {
    // Add transaction ID to the result
    final result = Map<String, dynamic>.from(event);
    result['transaction_id'] = initialization.transactionId;
    completer.complete(result);
  });

  return completer.future;
}
```

### startApplePayPayment

Initializes and processes an Apple Pay payment:

```dart
Future<Map<String, dynamic>> startApplePayPayment({
  required double amount,
  required String currency,
  required String cartDescription,
  required WalletChargeDto chargeDto,
}) async {
  // Initialize payment with backend
  final initialization = await paymentApi.initializePayment(chargeDto);

  // Create configuration
  final configuration = _createBaseConfiguration(
    amount: amount,
    currency: currency,
    cartDescription: cartDescription,
    cartId: initialization.transactionId,
  );

  // Set Apple Pay specific configuration
  configuration.merchantApplePayIndentifier = merchantApplePayIdentifier;
  configuration.simplifyApplePayValidation = true;

  // Create completer to handle async result
  final completer = Completer<Map<String, dynamic>>();

  // Start Apple Pay payment
  FlutterPaytabsBridge.startApplePayPayment(configuration, (event) {
    // Add transaction ID to the result
    final result = Map<String, dynamic>.from(event);
    result['transaction_id'] = initialization.transactionId;
    completer.complete(result);
  });

  return completer.future;
}
```

### \_createBaseConfiguration

Creates the base configuration for the PayTabs SDK:

```dart
PaymentSdkConfigurationDetails _createBaseConfiguration({
  required double amount,
  required String currency,
  required String cartDescription,
  required String cartId,
  BillingDetails? billingDetails,
}) {
  // Create configuration
  var configuration = PaymentSdkConfigurationDetails(
    profileId: profileId,
    serverKey: serverKey,
    clientKey: clientKey,
    cartId: cartId,
    cartDescription: cartDescription,
    merchantName: merchantName,
    screentTitle: "Payment",
    billingDetails: billingDetails,
    locale: PaymentSdkLocale.EN,
    amount: amount,
    currencyCode: currency,
    merchantCountryCode: merchantCountryCode,
  );

  // Configure theme
  var theme = IOSThemeConfigurations();
  theme.primaryColor = "#4E1D80"; // Purple
  theme.primaryFontColor = "#FFFFFF"; // White
  theme.primaryFont = "Tajawal-Bold";
  theme.secondaryFont = "Tajawal-Medium";
  configuration.iOSThemeConfigurations = theme;

  return configuration;
}
```

### \_createBillingDetails

Creates billing details from the user's profile:

```dart
BillingDetails? _createBillingDetails() {
  // Get current user
  final user = sl<AuthApi>().me();

  if (user == null) {
    return null;
  }

  // Create billing details
  return BillingDetails(
    name: user.name ?? "Customer",
    email: user.email ?? "<EMAIL>",
    phone: user.phone,
    addressLine: "Address Line",
    city: "City",
    state: "State",
    countryCode: user.countryCode,
    zip: "12345",
  );
}
```

## Usage Example

```dart
// In PaymentProvider
Future<void> processCardPayment({
  required BuildContext context,
  required WalletChargeDto chargeDto,
}) async {
  // Calculate VAT and total amount
  final vatAmount = chargeDto.amountPrice * 0.15;
  final totalAmount = chargeDto.amountPrice + vatAmount;

  // Start card payment
  final result = await paymentService.startCardPayment(
    amount: totalAmount,
    currency: "SAR",
    cartDescription: "Purchase ${chargeDto.amountPoint} points",
    chargeDto: chargeDto,
  );

  // Handle payment result
  _handlePaymentResult(context, result, chargeDto);
}
```
