<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PlaceResource;
use App\Models\Category;
use App\Models\Place;
use App\Models\SystemSetting;
use App\Models\UserRecommendationUsage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
class CategoryPlacesController extends Controller
{
    /**
     * Get places for a specific category
     *
     * @param Category $category
     * @return JsonResponse
     */
    public function __invoke(Request $request, Category $category): JsonResponse
    {
        // Get the authenticated user (if any)
        $user = $request->user();

        // Check if this is a "load more" request (recommendation tracking) vs regular pagination (browsing)
        // Extract from query string instead of request body
        $isLoadMore = $request->query('load_more', false);
        $isLoadMore = filter_var($isLoadMore, FILTER_VALIDATE_BOOLEAN);

        // Check if user is premium (has active subscription)
        $isPremium = $user && $user->hasActiveSubscription();

        // Only apply recommendation limits for load_more requests, not regular pagination
        // Regular pagination = browsing/viewing places (no usage tracking)
        // Load more = requesting additional recommendations (usage tracking applies)
        if ($isLoadMore && $user && !$user->is_anonymous && !$isPremium) {
            // Check if the user has reached their recommendation limit
            if ($user->hasReachedRecommendationLimit()) {
                // Get subscription info for limited user
                $subscriptionInfo = $this->getSubscriptionInfo($user, true);

                // Check if there are actually more places available in the database
                // This ensures the frontend can show the "Load More" button as a subscription prompt
                $placeId = $request->query('place_id');
                $hasMorePlaces = false;

                if ($placeId) {
                    $hasMorePlaces = Place::active()
                        ->where('category_id', $category->id)
                        ->where('id', '>', $placeId)
                        ->exists();
                } else {
                    // If no place_id provided, check if there are any places at all
                    $hasMorePlaces = Place::active()
                        ->where('category_id', $category->id)
                        ->exists();
                }

                // Return limited response with correct has_more flag
                return response()->json([
                    'data' => [],
                    'subscription_info' => $subscriptionInfo,
                    'has_more' => $hasMorePlaces,
                    'page' => 1,
                    'total_pages' => $this->getTotalPages($category->id, $request->query('per_page', 10)),
                ]);
            }

            // Increment the user's recommendation usage count (only for load_more requests)
            $user->incrementRecommendationUsage();
        }

        // Get the default limit from system settings
        $defaultLimit = SystemSetting::getValue(SystemSetting::KEY_DEFAULT_CATEGORY_PLACES_LIMIT, 10);
        $perPage = $request->query('per_page', $defaultLimit);

        // Build the query
        $query = Place::query()
            ->active()
            ->where('category_id', $category->id)
            ->with(['category', 'agent']);

        // Handle load more vs regular pagination
        if ($isLoadMore) {
            return $this->handleLoadMoreRequest($request, $query, $user, $isPremium, $category->id, $perPage);
        } else {
            return $this->handleRegularPagination($request, $query, $user, $isPremium, $perPage);
        }
    }

    /**
     * Handle load more request (single place with recommendation tracking)
     */
    private function handleLoadMoreRequest(
        Request $request,
        $query,
        $user,
        bool $isPremium,
        int $categoryId,
        int $perPage
    ): JsonResponse {
        $placeId = $request->query('place_id');
        $place = Place::find($placeId);

        if ($place) {
            // Get the next place after the specified place_id
            $query->where('id', '>', $placeId)->orderBy('id')->limit(1);
        }

        // Get the place
        $place = $query->first();

        if (!$place) {
            return response()->json([
                'data' => [],
                'subscription_info' => $this->getSubscriptionInfo($user, false, $isPremium),
                'has_more' => false,
                'page' => 1,
                'total_pages' => $this->getTotalPages($categoryId, $perPage),
            ]);
        }

        // Check if there are more places
        $hasMore = Place::active()
            ->where('category_id', $categoryId)
            ->where('id', '>', $place->id)
            ->exists();

        return response()->json([
            'data' => [PlaceResource::make($place)->resolve()],
            'subscription_info' => $this->getSubscriptionInfo($user, false, $isPremium),
            'has_more' => $hasMore,
            'page' => 1,
            'total_pages' => $this->getTotalPages($categoryId, $perPage),
        ]);
    }

    /**
     * Handle regular pagination (browsing - no usage tracking)
     */
    private function handleRegularPagination(
        Request $request,
        $query,
        $user,
        bool $isPremium,
        int $perPage
    ): JsonResponse {
        // Get current page from query string
        $page = $request->query('page', 1);

        // Paginate the results - NO usage tracking for regular pagination
        $places = $query->paginate($perPage, ['*'], 'page', $page);

        // Return all places without any recommendation limit restrictions
        // Regular pagination is for browsing, not for recommendations
        return response()->json([
            'data' => PlaceResource::collection($places->items())->resolve(),
            'subscription_info' => $this->getSubscriptionInfo($user, false, $isPremium),
            'has_more' => $places->hasMorePages(),
            'page' => $places->currentPage(),
            'total_pages' => $places->lastPage(),
        ]);
    }

    /**
     * Get subscription info for the response
     */
    private function getSubscriptionInfo($user, bool $isLimited = false, bool $isPremium = false): array
    {
        // For premium users, return null values
        if ($isPremium) {
            return [
                'limited' => false,
                'remaining' => null,
                'limit' => null,
                'next_reset' => null,
            ];
        }

        // For anonymous users or no user, return null values
        if (!$user || $user->is_anonymous) {
            return [
                'limited' => false,
                'remaining' => null,
                'limit' => null,
                'next_reset' => null,
            ];
        }

        // For registered non-premium users
        $remaining = UserRecommendationUsage::getRemainingCount($user);
        $limit = SystemSetting::getValue(SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED, 10);
        $usage = $user->recommendationUsage;
        $nextReset = $usage ? $usage->next_reset_at->toISOString() : null;

        return [
            'limited' => $isLimited || $remaining <= 0,
            'remaining' => max(0, $remaining),
            'limit' => $limit,
            'next_reset' => $nextReset,
        ];
    }

    /**
     * Get total pages for a category
     */
    private function getTotalPages(int $categoryId, int $perPage): int
    {
        $totalPlaces = Place::active()->where('category_id', $categoryId)->count();
        return (int) ceil($totalPlaces / $perPage);
    }
}
