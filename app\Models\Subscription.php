<?php

namespace App\Models;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'purchase_date' => 'datetime',
        'cancellation_date' => 'datetime',
        'points' => 'integer',
        'plan' => SubscriptionPlan::class,
        'status' => SubscriptionStatus::class,
        'auto_renewal' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function planDetails(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'plan', 'code');
    }

    public function paymentHistory(): HasMany
    {
        return $this->hasMany(PaymentHistory::class);
    }

    /**
     * Check if the subscription is currently active
     */
    public function isActive(): bool
    {
        return $this->status === SubscriptionStatus::ACTIVE &&
               $this->end_date > now();
    }

    /**
     * Check if the subscription can be reactivated
     */
    public function canReactivate(): bool
    {
        return in_array($this->status, [SubscriptionStatus::EXPIRED, SubscriptionStatus::CANCELED]) &&
               $this->end_date <= now();
    }

    /**
     * Get days until renewal
     */
    public function getDaysUntilRenewal(): ?int
    {
        if (!$this->isActive()) {
            return null;
        }

        return now()->diffInDays($this->end_date, false);
    }

    /**
     * Scope for active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('status', SubscriptionStatus::ACTIVE)
                    ->where('end_date', '>', now());
    }

    /**
     * Scope for expired subscriptions
     */
    public function scopeExpired($query)
    {
        return $query->where(function ($q) {
            $q->where('status', SubscriptionStatus::EXPIRED)
              ->orWhere('end_date', '<=', now());
        });
    }

    /**
     * Get the user's current active subscription
     */
    public static function getCurrentSubscription(int $userId): ?self
    {
        return static::where('user_id', $userId)
                    ->active()
                    ->latest('start_date')
                    ->first();
    }

    /**
     * Get the user's last subscription (for reactivation)
     */
    public static function getLastSubscription(int $userId): ?self
    {
        return static::where('user_id', $userId)
                    ->latest('end_date')
                    ->first();
    }

    /**
     * Check if the subscription is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === SubscriptionStatus::CANCELED;
    }

    /**
     * Get days until access ends
     */
    public function getDaysUntilAccessEnds(): ?int
    {
        // For cancelled subscriptions, show days until end_date
        if (!$this->isCancelled()) {
            return null;
        }

        return now()->diffInDays($this->end_date, false);
    }

    /**
     * Scope for cancelled subscriptions
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', SubscriptionStatus::CANCELED);
    }

    /**
     * Check if this subscription is a reactivation
     */
    public function isReactivation(): bool
    {
        // Check if there's a previous subscription for the same user
        return static::where('user_id', $this->user_id)
                    ->where('id', '<', $this->id)
                    ->whereIn('status', [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED])
                    ->exists();
    }

    /**
     * Get the previous subscription that this one reactivated from
     */
    public function getPreviousSubscription(): ?self
    {
        return static::where('user_id', $this->user_id)
                    ->where('id', '<', $this->id)
                    ->whereIn('status', [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED])
                    ->latest('end_date')
                    ->first();
    }

    /**
     * Get subscriptions that reactivated from this one
     */
    public function getReactivatedSubscriptions()
    {
        return static::where('user_id', $this->user_id)
                    ->where('id', '>', $this->id)
                    ->where('status', SubscriptionStatus::ACTIVE);
    }
}
