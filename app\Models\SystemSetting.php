<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'default_user_points' => 'integer',
        'points_refill_amount' => 'integer',
        'points_refill_interval_days' => 'integer',
        'last_refill_at' => 'datetime',
    ];

    // Points Settings
    public const KEY_DEFAULT_GUEST_POINTS = 'default_guest_points';
    public const KEY_DEFAULT_REGISTERED_POINTS = 'default_registered_points';
    public const KEY_GUEST_POINTS_RENEWAL_AMOUNT = 'guest_points_renewal_amount';
    public const KEY_GUEST_POINTS_RENEWAL_DAYS = 'guest_points_renewal_days';
    public const KEY_REGISTERED_POINTS_RENEWAL_AMOUNT = 'registered_points_renewal_amount';
    public const KEY_REGISTERED_POINTS_RENEWAL_HOURS = 'registered_points_renewal_hours';

    // Referral Settings
    public const KEY_REFERRAL_POINTS_PER_REFERRAL = 'referral_points_per_referral';
    public const KEY_REFERRAL_SYSTEM_ACTIVE = 'referral_system_active';

    // Social Media Settings
    public const KEY_SOCIAL_INSTAGRAM = 'social_instagram';
    public const KEY_SOCIAL_FACEBOOK = 'social_facebook';
    public const KEY_SOCIAL_TWITTER = 'social_twitter';

    // Recommendation Settings
    public const KEY_MAX_RECOMMENDATIONS = 'max_recommendations';
    public const KEY_RECOMMENDATION_LIMIT_REGISTERED = 'recommendation_limit_registered';
    public const KEY_RECOMMENDATION_RENEWAL_DAYS = 'recommendation_renewal_days';

    // Category Settings
    public const KEY_DEFAULT_CATEGORY_PLACES_LIMIT = 'default_category_places_limit';

    // Places Settings
    public const KEY_NEARBY_PLACES_DISTANCE = 'nearby_places_distance';

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        return match ($setting->type) {
            'integer' => (int) $setting->value,
            'boolean' => (bool) $setting->value,
            'float' => (float) $setting->value,
            'array', 'json' => json_decode($setting->value, true),
            default => $setting->value,
        };
    }

    /**
     * Fake a system setting for testing purposes.
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @param string $description
     * @param bool $isPublic
     * @return SystemSetting
     */
    public static function fake(string $key, $value, string $type = 'string', string $description = null, bool $isPublic = false): SystemSetting
    {
        return static::create([
            'key' => $key,
            'value' => $value,
            'type' => $type,
            'description' => $description,
            'is_public' => $isPublic,
        ]);
    }
}
